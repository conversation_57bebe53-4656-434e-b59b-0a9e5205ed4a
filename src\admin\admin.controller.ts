import { Controller, Get, Post, Body, Patch, Param, Delete, Response, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { Constants } from 'src/Constants';

const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('/getUserAccessToken')
  async getUserAccessToken(@Response() res, @Query('userKey') userKey: string) {
    let impersonatingUserId = res.locals.authorizedUserId;
    let userUniqueKey = userKey;
    let responseData = { 
      [responseTag]: await this.adminService.getUserAccessToken(userUniqueKey),
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getAllUsers')
  async getAllUsers(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.adminService.getAllUsers(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
