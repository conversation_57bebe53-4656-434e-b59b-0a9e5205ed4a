import { MiddlewareConsumer, Module } from '@nestjs/common';
import { ExternalApiController } from './external-api.controller';
import { PermissionMiddleware } from '@bryzos/base-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [ExternalApiController],
//   providers: OConstants.ServiceArray
// })
@Module({
  imports: [SharedModule],
  controllers: [ExternalApiController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class ExternalApiModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(PermissionMiddleware)
    .exclude('/external-api/v1/getAccessToken')
    .forRoutes('/external-api');
	}
}
