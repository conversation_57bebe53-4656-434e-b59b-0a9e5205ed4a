import { format, utcToZonedTime } from 'date-fns-tz';
import { <PERSON><PERSON><PERSON>sLogger} from '@bryzos/extended-widget-library';
const axios = require('axios');


export class Utility {

    public static getCtDateTime(dateTime,dateformat){
        const utcDate = utcToZonedTime(dateTime, 'America/Chicago');
        const formattedDate = format(utcDate, dateformat, { timeZone: 'America/Chicago' });
        return formattedDate;
    }
    
    public static sendDataToWebsocket = (object:object,endpoint:string)  => new Promise(async function (resolve, reject) 
    {
        let curlResponse = null;
        const websocketControllerEndPoint = process.env.GISS_WS_SERVER+'/'+endpoint;

        let updateData = {
            method: 'post',
            maxBodyLength: Infinity,
            url: websocketControllerEndPoint,
            headers: { 
                'accept': 'application/json', 
                'content-type': 'application/json', 
                'gissToken':  process.env.GISS_UI_TOKEN
            },
            data: object
        };
        axios.request(updateData)
        .then((response) => {
            curlResponse = response.data;
            BryzosLogger.log(JSON.stringify({"webScoketResponse":curlResponse}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
            resolve(curlResponse);
        })
        .catch((error) => {
            BryzosLogger.log(JSON.stringify({"Error":error}), process.env.LOGGLY_ERROR_TAG);
            resolve(curlResponse);
        });
    });
}