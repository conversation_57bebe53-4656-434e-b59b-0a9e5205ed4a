import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { SharedModule } from 'src/shared.module';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SuperAdminPermissionMiddleware } from '@bryzos/base-library';

// @Module({
//   controllers: [AdminController],
//   providers: [AdminService]
// })
@Module({
  imports: [SharedModule],
  controllers: [AdminController],
  providers: [SharedModule],
  exports: [SharedModule],
})
export class AdminModule {
  configure(consumer: MiddlewareConsumer) {
  consumer
    .apply(SuperAdminPermissionMiddleware, LoggerMiddleware)
    .forRoutes('/admin');
  }
}
