import { Controller, Get, Post, Body, Patch, Param, Delete, Request, Response } from '@nestjs/common';
import { ExternalAffairsService } from './external-affairs.service';
import { CreateExternalAffairDto } from './dto/create-external-affair.dto';
import { UpdateExternalAffairDto } from './dto/update-external-affair.dto';
import { Constants }  from '@bryzos/base-library';
import { Constants as localConstants} from 'src/Constants';
const responseTag = Constants.RESPONSE_TAG;
@Controller('external-affairs')
export class ExternalAffairsController {
  constructor(private readonly externalAffairsService: ExternalAffairsService) {}

  @Post()
  create(@Body() createExternalAffairDto: CreateExternalAffairDto) {
    return this.externalAffairsService.create(createExternalAffairDto);
  }

  @Get()
  findAll() {
    return this.externalAffairsService.findAll();
  }

  @Get('/getSellerDocuments')
  async getDocuments(@Request() req, @Response() res) {
    let responseData=null;
    if(req.headers['api-key'] && req.headers['api-key'] == process.env.TRUVALUT_ACCESS_KEY){
      responseData = await this.externalAffairsService.getAllDocuments(localConstants.TRUVAULT_DOC_TYPE_SELLER);
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }else{
      responseData={"data":"Invalid Key"};
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }
  @Get('/getBuyerDocuments')
  async getBuyerDocuments(@Request() req, @Response() res) {
    let responseData=null;
    if(req.headers['api-key'] && req.headers['api-key'] == process.env.TRUVALUT_ACCESS_KEY){
      responseData = await this.externalAffairsService.getAllDocuments(localConstants.TRUVAULT_DOC_TYPE_BUYER);
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);

    }else{
      responseData={"data":"Invalid Key"};
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }

  @Get('/mobile/selectedReleaseUrl')
  async getSelectedUrl(@Request() req, @Response() res) {
    let responseData = {
      [responseTag]: await this.externalAffairsService.getSelectedReleaseUrl()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
