import { Injectable } from '@nestjs/common';
import { CreateExternalAffairDto } from './dto/create-external-affair.dto';
import { UpdateExternalAffairDto } from './dto/update-external-affair.dto';
import { DataBaseService, Constants as LibConstants } from '@bryzos/base-library';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanyBuyNowPayLater, UserArPaymentInfo, ReferenceDataUiReleaseUrls } from '@bryzos/extended-widget-library';
import { Repository } from 'typeorm';
import { Constants } from 'src/Constants';

@Injectable()
export class ExternalAffairsService {
  private dbServiceObj = new DataBaseService()

      
  constructor(
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly cbnplRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(ReferenceDataUiReleaseUrls) private readonly referenceDataUiReleaseUrlsRepository: Repository<ReferenceDataUiReleaseUrls>,

  ){}
  create(createExternalAffairDto: CreateExternalAffairDto) {
    return 'This action adds a new externalAffair';
  }

  findAll() {
    return `This action returns all externalAffairs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} externalAffair`;
  }

  update(id: number, updateExternalAffairDto: UpdateExternalAffairDto) {
    return `This action updates a #${id} externalAffair`;
  }

  remove(id: number) {
    return `This action removes a #${id} externalAffair`;
  }

  async getAllDocuments(type: string) {
    let vaultUrl: string;
    let allDocuments = [];

    try {
      // Determine the base URL based on the document type
      if (type === Constants.TRUVAULT_DOC_TYPE_BUYER) {
        vaultUrl = process.env.TRUVALUT_BUYER_URL;
      } else {
        vaultUrl = process.env.TRUVALUT_SELLER_URL;
      }
  
      // Fetch the first page to determine the total number of items
      const initialData = await this.getTruvalutDocs(vaultUrl);

      if ( !initialData || typeof initialData !== "object" || !initialData.hasOwnProperty('data') || typeof initialData.data !== "object" || !initialData.data?.items ) {
        return { error_message: "Something went wrong!" };
      }
  
      // Add first page data to allDocuments
      if (initialData.data.items) {
        allDocuments.push(...initialData.data.items);
      }
  
      // Extract pagination details
      const { per_page, total } = initialData.data;
      const totalPages = Math.ceil(total / per_page);

      // Fetch remaining pages in parallel (start from page 2)
      const allPagePromises = [];
      for (let page = 2; page <= totalPages; page++) {
        const pageUrl = `${vaultUrl}&page=${page}`;
        allPagePromises.push(this.getTruvalutDocs(pageUrl));
      }
  
      const allPageData = await Promise.all(allPagePromises);
  
      // Process items from remaining pages
      for (const pageData of allPageData) {
        if (pageData?.data?.items) {
          allDocuments.push(...pageData.data.items);
        }
      }
  
      // Fetch all required paymentInfo in one batch
      const ids = allDocuments.map((data) => data.id);

      const paymentInfos = 
        type === Constants.TRUVAULT_DOC_TYPE_BUYER
          ? await this.dbServiceObj.findManyByWhereIn(this.cbnplRepository, 'reference_document_id', ids)
          : await this.dbServiceObj.findManyByWhereIn(this.userArPaymentInfoRepository, 'reference_document_id', ids);

      // Ensure paymentInfos is always an array
      const paymentInfoArray = paymentInfos || [];

      // Map paymentInfo by ID for fast lookup
      const paymentInfoMap = new Map(paymentInfoArray.map((info) => [info.reference_document_id, info]));

      // Process documents using reduce
      const processedDocuments = allDocuments.reduce((acc, data) => {
        const paymentInfo = paymentInfoMap.get(data.id);
        if (paymentInfo) {
          const document = {
            ...data,
            document: Buffer.from(data.document, 'base64').toString(),
            userId: paymentInfo['user_id'],
          };
          acc.push(document);
        }
        return acc;
      }, []);

      return processedDocuments;

    } catch (error) {
      console.log("Error in getAllDocuments: ", error);
      return { error_message: "Something went wrong while fetching documents." };
    }
  }
  
  getTruvalutDocs = async (vaultUrl: string): Promise<any> => {
    try {
      const axios = require("axios");
  
      const config = {
        method: "get",
        maxBodyLength: Infinity,
        url: vaultUrl,
        headers: {
          Authorization: "Basic " + process.env.TRUVALUT_API_KEY,
        },
      };
  
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.log("Error in getTruvalutDocs: ", error);
      throw new Error("Failed to fetch documents from TrueVault.");
    }
  }

  async getSelectedReleaseUrl(){
    const urlData = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataUiReleaseUrlsRepository,{"is_selected":true});
    if(!urlData){ return { error_message : "No url found" } }
    const isCapGoUrl = urlData.key === LibConstants.CAPGO_URL ? true : false;
    const response = {
      url : urlData.url,
      is_capgo : isCapGoUrl 
    }
    return response;
  }

}
