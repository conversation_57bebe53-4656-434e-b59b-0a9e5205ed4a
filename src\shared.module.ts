import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getConnectionOptions } from 'typeorm';
import { OConstants } from './OConstants';
import { BaseLibraryModule, DataBaseService } from '@bryzos/base-library';
import { PdfMakerModule } from '@bryzos/pdf-maker';
import { AwsUtility } from '@bryzos/extended-widget-library';

@Module({
  imports: [
    BaseLibraryModule,
    DataBaseService,
    PdfMakerModule,
    TypeOrmModule.forRootAsync({
      useFactory: async () =>
        Object.assign(await getConnectionOptions(), {
          autoLoadEntities: true,
        }),
    }),
    TypeOrmModule.forFeature(OConstants.EntityArray),
  ],
  providers: [
    BaseLibraryModule,
    AwsUtility,
    DataBaseService,
    ...OConstants.ServiceArray,
  ],
  exports: [
    BaseLibraryModule,
    DataBaseService,
    TypeOrmModule.forFeature(OConstants.EntityArray),
    ...OConstants.ServiceArray,
  ],
})

export class SharedModule {}
