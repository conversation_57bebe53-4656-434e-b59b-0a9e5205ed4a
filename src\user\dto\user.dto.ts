import { SwaggerErrorResponse, IsTrimmed } from "@bryzos/extended-widget-library";
import { ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Type, Transform } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsOptional, ValidateIf, IsDecimal, IsArray, ValidateNested, IsNumber, IsInt, ArrayMinSize,IsObject, IsJSON, ArrayNotEmpty, Min, IsBoolean, IsDate } from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from "src/Constants";

export class UserLoginDto {
    @ApiProperty() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() zip_code: string;
    @ApiProperty() @IsOptional()  @IsString() os_version : string;
    @ApiProperty() @IsOptional()  @IsString() ui_version : string;
    @ApiProperty() @IsOptional()  @IsString() last_login_app_version : string;
    @ApiProperty() @IsOptional()  @IsString() device_id : string;
}

export class LoginDto extends BaseDto {
    @Type(() => UserLoginDto)
    @ApiProperty() data: UserLoginDto;
}
export class PricingFeedbackDto {
    @ApiProperty() @IsNotEmpty() user_id: string;
    @ApiProperty() @IsNotEmpty() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() product_description: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum([Constants.HIGH_PRICING,Constants.LOW_PRICING,Constants.GOOD_PRICING], { message: 'Invalid feedback' }) feedback: string;
    @ApiProperty() @IsObject() prices : object;
    @ApiProperty() @IsNotEmpty() @IsString() price_feedback_type: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() @IsEnum([Constants.NEUTRAL,Constants.BUYER,Constants.SELLER], { message: 'Invalid user type' }) user_type?: string;
}

export class SavePricingFeedbackDto extends BaseDto {
    @Type(() => PricingFeedbackDto)
    @ApiProperty() data: PricingFeedbackDto;
}

export class ShareWidgetRequest {
    @ApiProperty() @IsNotEmpty() user_id: string;
    @ApiProperty() @IsNotEmpty() @IsEmail() from_email: string;
    @ApiProperty() @IsNotEmpty() @IsEmail() to_email: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() email_content: string;
    
}
export class SaveShareWidgetRequest extends BaseDto {
    @Type(() => ShareWidgetRequest)
    @ApiProperty() data: ShareWidgetRequest;
}

export class ShareWidgetRequestSwaggerDto {
    @ApiProperty({ nullable:true, oneOf: [{ type: 'string' }, { $ref: getSchemaPath(SwaggerErrorResponse) }] })
    data: string | SwaggerErrorResponse;
}

export class ProductPricing {
    @ApiProperty() @IsNotEmpty() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() product_description: string;
    @ApiProperty() @IsNotEmpty() @IsString() price_share_type: string;
    @ApiProperty() @IsDecimal() @ValidateIf((o) => ['ft'].includes(o.price_share_type)) @IsNotEmpty({message: 'Price per ft required',}) price_ft?: string;
    @ApiProperty() @IsDecimal() @ValidateIf((o) => ['lb'].includes(o.price_share_type)) @IsNotEmpty({message: 'Price per Lb required',}) price_lb?: string;
}

export class ShareProductPricing {
    @ApiProperty() @IsNotEmpty() user_id: string;
    @ApiProperty() @IsNotEmpty() @IsEmail() from_email: string;
    @ApiProperty() @IsNotEmpty() @IsEmail() to_email: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() email_content: string;
    @ApiProperty({ type: () => [ProductPricing] }) @IsArray() 
    @ValidateNested({ each: true }) @Type(() => ProductPricing) products: ProductPricing[];
    from_user_type: string;
}
export class SaveShareProductPricing extends BaseDto {
    @Type(() => ShareProductPricing)
    @ApiProperty() data: ShareProductPricing;
}

export class ShareProductPricingSwaggerDto {
    @ApiProperty({ nullable:true, oneOf: [{ type: 'string' }, { $ref: getSchemaPath(SwaggerErrorResponse) }] })
    data: string | SwaggerErrorResponse;
}

export class WidgetTermsCondtionUserActionsDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() bryzos_terms_condtion_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() terms_conditions_version: string;
    @ApiProperty() @IsNotEmpty() @IsString() email_id: string;
}
export class SaveWidgetTermsCondtionUserActionsDto extends BaseDto {
    @Type(() => WidgetTermsCondtionUserActionsDto)
    @ApiProperty() data: WidgetTermsCondtionUserActionsDto;
}

export class GetSignedUrl {
    @ApiProperty() @IsNotEmpty() @IsString() bucket_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() object_key: string;
    @ApiProperty() @IsNotEmpty() @IsInt() expire_time: number;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() actual_filename: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() user_name: string;
    @ApiProperty() @IsOptional() @IsString() caption: string;
    @ApiProperty() @IsOptional() @IsString() company_name: string;

}
export class SignedUrl extends BaseDto {
    @Type(() => GetSignedUrl)
    @ApiProperty() data: GetSignedUrl;
}

export class PoS3UrlsDto {
    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty() @IsArray() @ArrayMinSize(1) upload_po_s3_urls: any;
}
export class SavePoS3UrlsDto extends BaseDto {
    @Type(() => PoS3UrlsDto)
    @ApiProperty() data: PoS3UrlsDto;
}

export class StatezipDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() state_id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() zip_code: number;
}
export class SaveStatezipDto extends BaseDto {
    @Type(() => StatezipDto)
    @ApiProperty() data: StatezipDto;
}
export class OnBoardDto {
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum([Constants.BUYER,Constants.SELLER], { message: 'Invalid user type' }) user_type: string;
    @ApiProperty() @IsNotEmpty() @IsString() @Transform(({ value }) => value.replace(/<\/?[^>]+(>|$)/g, '').trim()) company_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() first_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() last_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEmail() email_id: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() bryzos_terms_condtion_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() accepted_terms_and_condition: string;
    @ApiProperty() @IsNotEmpty() @IsString() zip_code: string;
    @ApiProperty() @IsNotEmpty() @IsString() client_company: string;
}
export class SaveOnBoardDto extends BaseDto {
    @Type(() => OnBoardDto)
    @ApiProperty() data: OnBoardDto;
}

export class UserEmailDto {
    @ApiProperty() @IsNotEmpty() @IsString() @IsEmail() email_id: string;
}

export class VerifyUserEmailDto extends BaseDto {
    @Type(() => UserEmailDto)
    @ApiProperty() data: UserEmailDto;
}

export class TooltipDto{
    @ApiProperty() tooltips: string; 
}   

export class SaveTooltipDto extends BaseDto {
    @Type(() => TooltipDto)
    @ApiProperty() data:TooltipDto;
}

export class ZipCodeDto {
    @ApiProperty() @IsNotEmpty() @IsString() zip_code: string;
}

export class VerifyZipCodeDto extends BaseDto {
    @Type(() => ZipCodeDto)
    @ApiProperty() data: ZipCodeDto;
}

export enum ForegroundBackgroundActivities {
    OUT_OF_FOCUS = "OUT_OF_FOCUS",
    IN_FOCUS = "IN_FOCUS",
    MOVE_TO_BACKGROUND = "MOVE_TO_BACKGROUND",
    MOVE_TO_FOREGROUND = "MOVE_TO_FOREGROUND",
    APPLICATION_CLOSED = "APPLICATION_CLOSED",
    SOCKET_DISCONNECTED = "SOCKET_DISCONNECTED",
    SOCKET_CONNECTED = "SOCKET_CONNECTED",
}

export class ForegroundBackgroundActivityDto {
    @ApiProperty() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum(ForegroundBackgroundActivities) event: string;
    @ApiProperty() @IsOptional() @IsString() os_version: string;
    @ApiProperty() @IsOptional() @IsString() ui_version: string;
    @ApiProperty() @IsOptional() @IsString() last_login_app_version: string;
    @ApiProperty() @IsOptional() @IsString() device_id: string;
}
export class SaveForegroundBackgroundActivityDto extends BaseDto {
    @Type(() => ForegroundBackgroundActivityDto)
    @ApiProperty() data: ForegroundBackgroundActivityDto;
}


export class CreateUserDto {
    @ApiProperty() @IsNotEmpty() @IsString() first_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() last_name: string;
    @ApiProperty() @IsTrimmed() @IsNotEmpty() @IsString() company_name: string;
    @ApiProperty() @IsTrimmed() @IsNotEmpty() @IsString() client_company: string;
    @ApiProperty() @IsNotEmpty() @IsString() email_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() user_type: string;
    @ApiProperty() @IsNotEmpty() @IsString() cognito_user_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() accepted_terms_and_condition: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() bryzos_terms_condtion_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() zip_code: string;
    @ApiProperty() @IsOptional()  @IsString() last_login_app_version : string;
    @ApiProperty() @IsOptional()  @IsString() os_version : string;
    @ApiProperty() @IsOptional()  @IsString() device_id : string;
    @ApiProperty() @IsOptional()  @IsString() ui_version : string;  
}

export class UserSignUpDto extends BaseDto {
    @Type(() => CreateUserDto)
    @ApiProperty() data: CreateUserDto;
}

export class ConfirmSignupDto {
    @ApiProperty() @IsNotEmpty() @IsString() username: string;
}

export class ConfirmSignupDataDto extends BaseDto {
    @Type(() => ConfirmSignupDto)
    @ApiProperty() data: ConfirmSignupDto;
}

export class MigrateToPasswordLessDto {
    @ApiProperty() @IsNotEmpty() @IsEmail() email_id: string;
}

export class SaveMigrateToPasswordLessDto extends BaseDto {
    @Type(() => MigrateToPasswordLessDto)
    @ApiProperty() data: MigrateToPasswordLessDto;
}

export class GlobalSignoutDto {
    @ApiProperty() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() device_id: string;
}

export class UserGlobalSignoutDto extends BaseDto {
    @Type(() => GlobalSignoutDto)
    @ApiProperty() data: GlobalSignoutDto;
}

export class GetVideosDto {
    @IsString()
    @IsNotEmpty({ message: 'Tag parameter is required' })
    tag: string;
}

export class VideoViewDto {
    @ApiProperty() @IsNotEmpty() @IsString() user_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() video_id: string;
}
export class SaveVideoViewDto extends BaseDto {
    @Type(() => VideoViewDto)
    @ApiProperty() data: VideoViewDto;
}

export class ShareVideoAnalyticsDto {
    @ApiProperty() @IsNotEmpty() @IsString() share_via: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() receiver: string;
    @ApiProperty() @IsNotEmpty() @IsString() video_id: string;
}
export class SaveShareVideoAnalyticsDto extends BaseDto {
    @Type(() => ShareVideoAnalyticsDto)
    @ApiProperty() data: ShareVideoAnalyticsDto;
}

export class ExternalUserLoginDto {
    @ApiProperty() @IsNotEmpty() @IsEmail() email: string;
    @ApiProperty() @IsNotEmpty() @IsString() password: string;
}

export class ExternalLoginDto extends BaseDto {
    @Type(() => ExternalUserLoginDto)
    @ApiProperty() data: ExternalUserLoginDto;
}

export class ShippingDetailsDto {
    @ApiProperty() @IsNotEmpty() @IsString() line1: string;
    @ApiProperty({ required: true }) @IsString() line2: string;
    @ApiProperty() @IsNotEmpty() @IsString() city: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @IsString() state_id: number | string;
    @ApiProperty() @IsNotEmpty() @IsString() zip: string;
}
export class BomUpload {
    @ApiProperty() @IsString() @IsNotEmpty() actual_file_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() s3_url: string;
    @ApiProperty() @IsString() @IsNotEmpty() object_key: string;
    @ApiProperty() @IsString() @IsOptional() @IsNotEmpty() unique_identifier: string;
    @ApiProperty() @IsString() @IsNotEmpty() device_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() bom_name: string;
    @ApiProperty() @IsNotEmpty() @IsEnum(["BID","BUY"], {message: "Invalid bom type"}) bom_type: string;
    @ApiProperty() @IsNotEmpty() @IsDate() @Type(() => Date) delivery_date: string;
    @ApiProperty() @IsNotEmpty() @IsObject() shipping_details: ShippingDetailsDto;
}
export class SaveBomUpload extends BaseDto{
    @Type(() => BomUpload)
    @ApiProperty() data : BomUpload
}

export class SelectedBomProducts {
    @ApiProperty() @IsString() @IsNotEmpty() id: string;
    @ApiProperty() @IsArray() @IsNumber({}, { each: true }) selected_products: number[];
    @ApiProperty() @IsArray() @IsNumber({}, { each: true }) matched_products: number[];
    @ApiProperty() @IsNumber() @IsNotEmpty() product_status_id: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() product_index: number;
    @ApiProperty() @IsString() @IsOptional() product_tag: string;
    @ApiProperty() @IsString() @IsOptional() description: string;
    @ApiProperty() @IsString() @IsOptional() specification: string;
    @ApiProperty() @IsString() @IsOptional() grade: string;
    @ApiProperty() @IsString() @IsOptional() weight_per_quantity: string;
    @ApiProperty() @IsString() @IsOptional() length: string;
}
export class SaveSelectedBomProducts extends BaseDto{
    @Type(() => SelectedBomProducts)
    @ApiProperty() @ArrayNotEmpty() @IsNotEmpty() @IsArray() @ValidateNested({ each: true }) @Type(() => SelectedBomProducts) data : SelectedBomProducts[];
}

export class GameScore {
    @ApiProperty() @IsInt() @Min(0) @IsNotEmpty() score: number;
    @ApiProperty() @IsInt() @Min(0) @IsNotEmpty() consecutive_catches: number;
}
export class SaveGameScore extends BaseDto{
    @Type(() => GameScore)
    @ApiProperty() data : GameScore
}

export class BomDraftDto {
    @ApiProperty() @IsString() @IsNotEmpty() id: string;
    @ApiProperty() @IsOptional() @IsNumber() selected_product_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() status: string;
    @ApiProperty() @IsString() @IsOptional() qty: string;
    @ApiProperty() @IsString() @IsNotEmpty() qty_unit: string;
    @ApiProperty() @IsNotEmpty() @IsBoolean() @Transform(({ value }) => value === 1 ? true : value === 0 ? false : value) domestic_material_only: boolean;
    @ApiProperty() @IsOptional() @IsString() @ValidateIf((object, value) => value !== null) product_tag: string;


}
export class SaveBomDraftDto extends BaseDto {
    @Type(() => BomDraftDto)
    @ApiProperty() data: BomDraftDto;
}
export class BomSummaryDto {
    @ApiProperty() @IsString() @IsNotEmpty() bom_upload_id: string;
}
export class SaveBomSummaryDto extends BaseDto{
    @Type(() => BomSummaryDto)
    @ApiProperty() data : BomSummaryDto;
}
export class BomHeaderDetailsDto {
    @ApiProperty() @IsString() @IsNotEmpty() bom_upload_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() bom_name: string;
    @ApiProperty() @IsOptional() @IsEnum(["BID","BUY"], {message: "Invalid bom type"}) bom_type: string;
    @ApiProperty() @IsNotEmpty() @IsDate() @Type(() => Date) delivery_date: string;
    @ApiProperty() @IsNotEmpty() @IsObject() shipping_details: ShippingDetailsDto;
}
export class SaveBomHeaderDetailsDto extends BaseDto{
    @Type(() => BomHeaderDetailsDto)
    @ApiProperty() data: BomHeaderDetailsDto;
}
