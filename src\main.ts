import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from '@bryzos/extended-widget-library';
import { SecurityMiddleware } from '@bryzos/base-library';
import { json, urlencoded, Request, Response, NextFunction } from 'express';
import { ValidationPipe } from '@nestjs/common';

const EXCLUDED_PATHS = ['/external-affairs/getSellerDocuments','/external-affairs/getBuyerDocuments','/reference-data/getSecurityToken','/user/purchaseOrderExcel','/user/payableStatementExcel', '/user/salesOrderExcel', '/user/receivableStatementExcel', '/textExtract/sellerInvoice', '/user/shuffleSequence', '/reference-data/product/search'];


function applyBodyParsing(req: Request, res: Response, next: NextFunction) {
	// If we already have the parsed body, continue
	if (req.body) {
		return next();
	}
  
	// If we have the raw body from previous middleware, reconstruct the stream
	if ((req as any).rawBody) {
		const newStream = require('stream').Readable.from([(req as any).rawBody]);
		Object.assign(req, newStream);
	}
  
	json({
		limit: `${process.env.REQUEST_JSON_SIZE}mb`,
		verify: (req: any, _res, buf) => {
			req.rawBody = buf;
		}
	})(req, res, (err) => {
	  if (err) {
		return next(err);
	  }
	urlencoded({ 
		limit: `${process.env.REQUEST_JSON_SIZE}mb`, 
		extended: true 
	})(req, res, next);
	});
}

function createCombinedMiddleware(app) {
	const securityMiddleware = new SecurityMiddleware(
		app.get('ReferenceDataSettingsRepository')
	);

	return (req: Request, res: Response, next: NextFunction) => {
		try {
			if (EXCLUDED_PATHS.includes(req.path)) {
				// Only excluded paths bypass security
				applyBodyParsing(req, res, next);
			} else {
				// For all other paths, collect the raw body first
				let rawBody = Buffer.from([]);
				
				// Store the original stream handlers
				const originalOn = req.on.bind(req);
				const originalOnce = req.once.bind(req);
				
				// Collect the body
				req.on('data', chunk => {
				rawBody = Buffer.concat([rawBody, chunk]);
				});

				req.once('end', () => {
				try {
					// Store raw body
					(req as any).rawBody = rawBody;
					
					// Restore original stream handlers
					req.on = originalOn;
					req.once = originalOnce;
					
					// Create new readable stream from raw body
					const newStream = require('stream').Readable.from([rawBody]);
					Object.assign(req, newStream);

					// Run security middleware
					securityMiddleware.use(req, res, (err) => {
					if (err) {
						return next(err);
					}
					// Then parse body
					applyBodyParsing(req, res, next);
					});
				} catch (error) {
					next(error);
				}
				});

				req.on('error', (error) => {
				next(error);
				});
			}
		} catch (error) {
			console.error('Middleware error:', error);
			next(error);
		}
	};
}

async function bootstrap() {
	const app = await NestFactory.create(AppModule, { });

	const configService = app.get(ConfigService);

	if (process.env.NODE_ENV == 'development') {
		const swagger_config = new DocumentBuilder()
		.setTitle('Bryzos widget Service')
		.setDescription( 'The widget Service APIs are used for onboarding and Purchase orders', )
		.setVersion('1.0')
		.addBearerAuth()
		.addServer(process.env.SERVER_URL)
		.addTag('node-widget-service')
		.build();
		const document = SwaggerModule.createDocument(app, swagger_config);

		document.components.securitySchemes = {
		accesstoken: {
			type: 'apiKey',
			in: 'header',
			name: 'accesstoken',
			description: 'A Custom Header',
		},
		};

		SwaggerModule.setup('api', app, document);
	}

	app.enableCors();

	// Apply SecurityMiddleware before body-parser
	// app.use(createCombinedMiddleware(app));
	app.useGlobalPipes(new ValidationPipe());

	app.useGlobalFilters(new GlobalExceptionFilter());

	app.listen(configService.get<string>('PORT'), () => console.log(`Listening on port `, configService.get<string>('PORT')), );
}

bootstrap();
