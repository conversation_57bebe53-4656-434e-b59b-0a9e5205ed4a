import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AwsQueue } from 'src/AwsQueue';
import { Constants } from 'src/Constants';
import { Repository } from 'typeorm';
import { AwsUtilityV3, DataBaseService, Constants as LibConstants } from '@bryzos/base-library';
import { HomePageShareProductPricing, HomePagePricingFeedbackDto, ImageCommentDto, ReactionDto } from './dto/home-page.dto';
import { HomepagePricingFeedback,HomepageSearchAnalytics,HomepageShareWidgetRequest,HomepageShareProductPricing, HomepageSafeUploads, HomepageSafeUploadComments, HomepageSafeUploadReactionLog } from '@bryzos/extended-widget-library'
import { GetSignedUrl } from 'src/user/dto/user.dto';
@Injectable()
export class HomePageService {
  private dbServiceObj = new DataBaseService()

  constructor(
    private readonly awsQueue:AwsQueue,
    private readonly awsUtility:AwsUtilityV3,

    @InjectRepository(HomepagePricingFeedback) private readonly homePagePricingFeedback: Repository<HomepagePricingFeedback>,
    @InjectRepository(HomepageShareWidgetRequest) private readonly homepageShareWidgetRequest: Repository<HomepageShareWidgetRequest>,
    @InjectRepository(HomepageShareProductPricing) private readonly homepageShareProductPricing: Repository<HomepageShareProductPricing>,
    @InjectRepository(HomepageSearchAnalytics) private readonly homepageSearchAnalyticsRepository: Repository<HomepageSearchAnalytics>,
    @InjectRepository(HomepageSafeUploads) private readonly homepageSafeUploads: Repository<HomepageSafeUploads>,
    @InjectRepository(HomepageSafeUploadComments) private readonly homepageSafeUploadComments: Repository<HomepageSafeUploadComments>,
    @InjectRepository(HomepageSafeUploadReactionLog) private readonly homepageSafeUploadReactionLog: Repository<HomepageSafeUploadReactionLog>,
  
    ) {}
    
  async homePageShareProductPricing (homepageShareProductPricingDto: HomePageShareProductPricing) {
    let response = null;
      if(homepageShareProductPricingDto.to_email == homepageShareProductPricingDto.from_email) return { "error_message": "To and from email cannot be same" };
      let shareProductPricing = await this.dbServiceObj.saveData(homepageShareProductPricingDto,this.homepageShareProductPricing);
      if(shareProductPricing) {
        await this.awsQueue.sendDataToAwsQueue(shareProductPricing.id,Constants.HOMEPAGE_SHARE_PRODUCT_PRICING,"Home-page share product pricing");
        response = 'Homepage Product prices shared successfully'
      }
    return response;
  }

  async homePageSavePricingFeedback(homePagePricingFeedbackDto: HomePagePricingFeedbackDto) {
    let response = null;
      let feedback = await this.dbServiceObj.saveData(homePagePricingFeedbackDto,this.homePagePricingFeedback);
      if(feedback) {
        response = "Feedback noted";
      }
    return response;
  }

  async homePageSaveSearchAnalytics(payload){
    if(!payload.status){
      payload.status = Constants.NONE;
    }
    await this.dbServiceObj.saveData(payload,this.homepageSearchAnalyticsRepository);
  }

  async getHomepageImageCarousel() {
    let homepageCarousel = await this.dbServiceObj.getLatestData(this.homepageSafeUploads, {show_on_carousel: true}, 'created_date', 20);

    if (homepageCarousel.length === 0) {
        return null;
    }

    let homepageComments = await this.dbServiceObj.getLatestData(this.homepageSafeUploadComments, {show_comment: true}, 'created_date', null);

    // Create a map of homepageComments by homepage_safe_uploads_id for efficient lookup
    const homepageCommentsMap = homepageComments.reduce((map, comment) => {
        if (!map.has(comment.homepage_safe_uploads_id)) {
            map.set(comment.homepage_safe_uploads_id, []);
        }
        map.get(comment.homepage_safe_uploads_id).push(comment);
        return map;
    }, new Map());

    // Map homepageCarousel to include comments
    const homepageCarouselWithComments = homepageCarousel.map(carousel => ({
        ...carousel,
        comments: homepageCommentsMap.get(carousel.id) || [],
    }));

    return homepageCarouselWithComments;
  }

  async generateSafePagePreSignedUrl(dto:GetSignedUrl) {
    let getSignedS3Url = await this.awsUtility.getSignedS3Url(dto.object_key,dto.bucket_name,dto.expire_time);    

    if(getSignedS3Url) {

      let caption = dto.caption || null;
      let company_name = dto.company_name || null;
      
      const pathParts = dto.object_key.split('/');
      const fileName = 'https://extended-widget-uploads.s3.amazonaws.com/'+process.env.S3_BUCKET_ENVIRONMENT+"/safepage/"+pathParts[pathParts.length - 1];
      await this.dbServiceObj.saveData( { "safepage_url_s3": fileName, "actual_filename": dto.actual_filename , "user_name": dto.user_name, "caption": caption, "company_name": company_name}, this.homepageSafeUploads);
    }

    return getSignedS3Url;
  }

  async saveImageComment(dto:ImageCommentDto) {
    let response = null;

    const commentAdded = await this.dbServiceObj.saveData( { "user_name": dto.user_name, "homepage_safe_uploads_id": dto.homepage_safe_uploads_id, "comment": dto.comment}, this.homepageSafeUploadComments);

    if(commentAdded){
      response = "Comment added successfully";
    }
    return response;
  }

  async saveIncreaseReaction(payload:ReactionDto){
    try{
      let updateDto = {}
      if(payload.reaction === LibConstants.THUMBS_UP){
        updateDto["thumbs_up_count"] = () => `thumbs_up_count + 1`;
      }else if(payload.reaction === LibConstants.HEART){
        updateDto["heart_count"] = () => `heart_count + 1`;
      }

      this.dbServiceObj.updateByMultipleWhere(updateDto,{ id : payload.homepage_safe_upload_id },this.homepageSafeUploads);
      this.dbServiceObj.saveData(payload,this.homepageSafeUploadReactionLog)
      return "Reaction saved successfully"
    }catch(error){
      console.log(error);
      return { error_message : "Something went wrong!!" }
    }
  }

  async saveDecreaseReaction(payload:ReactionDto){
    try{
      let updateDto = {}
      if(payload.reaction === LibConstants.THUMBS_UP){
        updateDto["thumbs_up_count"] = () => `thumbs_up_count - 1`;
      }else if(payload.reaction === LibConstants.HEART){
        updateDto["heart_count"] = () => `heart_count - 1`;
      }

      this.dbServiceObj.updateByMultipleWhere(updateDto,{ id : payload.homepage_safe_upload_id },this.homepageSafeUploads);
      this.dbServiceObj.markInActiveMultipleWhere(this.homepageSafeUploadReactionLog,payload);
      return "Reaction saved successfully"
    }catch(error){
      console.log(error);
      return { error_message : "Something went wrong!!" }
    }
  }

  async getSafeReactionData(userName:string){
    const reactions = await this.dbServiceObj.selectCustomFieldsOrderByCreatedDate(this.homepageSafeUploadReactionLog,"user_name",userName,"*","DESC");
    let response = {};
    for(let reactionData of reactions){
      if(!response.hasOwnProperty(reactionData.homepage_safe_upload_id)){
        response[`${reactionData.homepage_safe_upload_id}`] = {};
        response[`${reactionData.homepage_safe_upload_id}`][`${reactionData.reaction}`] = true
      }else{
        response[`${reactionData.homepage_safe_upload_id}`][`${reactionData.reaction}`] = true
      } 
    }
    if(Object.keys(response).length === 0){ return null };
    return response; 
  }
}
