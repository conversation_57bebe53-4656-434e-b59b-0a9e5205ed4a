import { MiddlewareConsumer, Module } from '@nestjs/common';
import { ReferenceDataController } from './reference-data.controller';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { AdminPermissionMiddleware, PermissionMiddleware } from '@bryzos/base-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [ReferenceDataController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [ReferenceDataController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class ReferenceDataModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware, AdminPermissionMiddleware)
      .forRoutes('/reference-data/referenceProductUpload', '/reference-data/referenceProductUpdate', '/reference-data/getCache', '/reference-data/updateCache', '/reference-data/deleteCache');
    consumer
      .apply(PermissionMiddleware)
      .exclude('/reference-data/homepage', '/reference-data/getHomepageProductData','/reference-data/generateSafePagePreSignedUrl', 'reference-data/getHomepageSafeConfig', 'reference-data/getSecurityToken','reference-data/getVideoLibraryTags')
      .forRoutes('/reference-data/getAllProducts', '/reference-data');
    consumer
      .apply(LoggerMiddleware)
      .forRoutes('/reference-data/getHomepageProductData','/reference-data/product/search');

  }
}
