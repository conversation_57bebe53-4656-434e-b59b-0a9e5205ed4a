import { Controller, Get, Post, Body, Patch, Param, Delete, Response, ValidationPipe, UsePipes, Query } from '@nestjs/common';
import { HomePageService } from './home-page.service';
import { SaveHomePageShareProductPricing, SaveHomePagePricingFeedbackDto, SaveImageCommentDto, SaveReactionDto, GetHomePageReactionData } from './dto/home-page.dto';
import { Constants } from 'src/Constants';
import { SignedUrl } from 'src/user/dto/user.dto';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('homepage')
export class HomePageController {
  constructor(private readonly homePageService: HomePageService) {}

  @Post('/shareProductPrice')
  @UsePipes(ValidationPipe)
  async homePageShareProductPricing(@Body() homepageShareProductPricingDto: SaveHomePageShareProductPricing, @Response() res) {
    let payloadData = homepageShareProductPricingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageShareProductPricing(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/savePricingFeedback')
  @UsePipes(ValidationPipe)
  async homePageSavePricingFeedback(@Body() homePagePricingFeedback: SaveHomePagePricingFeedbackDto, @Response() res) {
    let payloadData = homePagePricingFeedback[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageSavePricingFeedback(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/searchAnalytics')
  async homePageSearch(@Body() payloadData, @Response() res) {
    let payload = payloadData[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageSaveSearchAnalytics(payload)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getImageCarousel')
  async getHomepageImageCarousel(@Response() res) {
    let responseData = {
      [responseTag]: await this.homePageService.getHomepageImageCarousel()
    };
    //res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/generateSafePagePreSignedUrl')
  @UsePipes(ValidationPipe)
  async getSignedS3Url(@Body() dto: SignedUrl,@Response() res) {
    let payloadData = dto[payloadTag];

    let responseData = {
      [responseTag]: await this.homePageService.generateSafePagePreSignedUrl(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/addImageComment')
  @UsePipes(ValidationPipe)
  async saveImageComment(@Body() dto: SaveImageCommentDto,@Response() res) {
    let payloadData = dto[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.saveImageComment(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/increaseReactionCount')
  @UsePipes(ValidationPipe)
  async increaseReactionCount(@Body() saveReactionDto: SaveReactionDto,@Response() res) {
    let payloadData = saveReactionDto[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.saveIncreaseReaction(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/decreaseReactionCount')
  @UsePipes(ValidationPipe)
  async decreaseReactionCount(@Body() saveReactionDto: SaveReactionDto,@Response() res) {
    let payloadData = saveReactionDto[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.saveDecreaseReaction(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getSafeReactions')
  @UsePipes(ValidationPipe)
  async getSafeReactionData(@Query() query:GetHomePageReactionData,@Response() res) {
    let responseData = {
      [responseTag]: await this.homePageService.getSafeReactionData(query.user_name)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
