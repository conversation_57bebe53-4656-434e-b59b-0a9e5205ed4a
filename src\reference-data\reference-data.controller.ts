import { Controller, Get, Post, Body, Param, Delete, Request, Response, Query, ValidationPipe, UsePipes } from '@nestjs/common';
import { ReferenceDataService } from './reference-data.service';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetCacheDataDto, producSearchDto } from './dto/update-reference-datum.dto';
import { Constants } from 'src/Constants';
import { ReferenceDataProductsSwaggerEntity, ReferenceDataProductsSwaggerResponse, ReferenceDatum } from '@bryzos/extended-widget-library';
import { SignedUrl } from 'src/user/dto/user.dto';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@ApiTags('Reference Products')
@Controller('reference-data')
export class ReferenceDataController {
  constructor(private readonly referenceDataService: ReferenceDataService) {}

  @ApiResponse({
    description: 'This is reference data',
    type: ReferenceDataProductsSwaggerEntity,
    status: 200
  })
  @Get('/getAllProducts')
  async findAllProducts(@Response() res) {
    let responseData = {
      [responseTag]: await this.referenceDataService.findAllProducts()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'This is reference data',
    type: ReferenceDatum,
    status: 200
  })
  @Get()
  findAll() {
    return this.referenceDataService.findAll();
  }

  @Get('/homepage')
  getHomepageReferenceData() {
    return this.referenceDataService.getHomepageReferenceData();
  }

  @ApiResponse({
    description: 'Upload reference data',
    type: ReferenceDataProductsSwaggerResponse,
    status: 200
  })
  @Post('referenceProductUpload')
  async uploadFile(@Body() referencedata ,@Response() res) {
    let payloadData = referencedata[payloadTag];
    let responseData = {
      [responseTag]: await this.referenceDataService.fileUploadData(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }


  @ApiResponse({
    description: 'This is reference data',
    type: ReferenceDatum,
    status: 200
  })
  @Post('/getCache')
  getCache(@Body() referencedata:GetCacheDataDto) {
    let payloadData = referencedata[payloadTag];
    return this.referenceDataService.getCacheData(payloadData);
  }
  
 
  @ApiResponse({
    description: 'Update reference data',
    type: ReferenceDatum,
    status: 200
  })
  @Post('/updateCache')
  updateCache(@Body() referencedata:GetCacheDataDto) {
    let payloadData = referencedata[payloadTag];  
    return this.referenceDataService.updateCache(payloadData)
  }


  @ApiResponse({
    description: 'Delete reference data',
    type: ReferenceDatum,
    status: 200
  })
  @Delete('/deleteCache')
  deleteCache(@Body() referencedata:GetCacheDataDto) {
    let payloadData = referencedata[payloadTag];
    return this.referenceDataService.deleteCache(payloadData);
  }
  
  @Get('previewConfirm/:action/:version')
  async getByPOnumber(@Param('action') action: string, @Param('version') version: string, @Request() req, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.referenceDataService.previewAction(action, userId, accessToken, version)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('revertReferenceProducts/:action')
  async revertReferenceProducts(@Param('action') action: string, @Request() req, @Response() res){
    let userId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.referenceDataService.revertReferenceProducts(action,userId, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getReferenceDataVersions')
  async getReferenceDataVersion(@Response() res){
    let responseData = {
      [responseTag]: await this.referenceDataService.getRefDatasVersion()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getHomepageProductData')
  async getHomePageProducts(@Response() res) {
    let responseData = {
      [responseTag]: await this.referenceDataService.getHomePageProductReferenceData()
    };
    //res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Get('/product/search')
  @UsePipes(ValidationPipe)
  async searchProducts(@Query() query:producSearchDto, @Response() res) {
    
    const userId = res.locals.authorizedUserId;
    const responseData = {
      data: await this.referenceDataService.searchProducts(query.keyword, userId),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getHomepageSafeConfig')
  async getHomepageSafeConfig(@Response() res) {
    let responseData = {
      [responseTag]: await this.referenceDataService.getHomepageSafeConfig()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getSecurityToken')
  async getSecurityToken(@Response() res) {
    let responseData = {
      [responseTag]: await this.referenceDataService.getSecurityToken()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getVideoLibraryTags')
  async getVideoLibraryTags(@Response() res) {
    let responseData = {
      [responseTag]: await this.referenceDataService.getVideoLibraryTags()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
