import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataBaseService, ReferenceDataSettings} from '@bryzos/base-library';
import { Repository } from 'typeorm';
import { BuyingPreferenceDto, BuyerProfileDto, BuyerCompanyInfoDto, BuyerDeliveryDto, DeliveryAvailibilityDto, BnplRequestDto, ResaleCertificateDto, IncreaseCreditLimitDto, depositAmountDto } from '../dto/save-buying-preference.dto';
import { UserBuyingPreferenceMapper, UserDeliveryReceivingAvailibitityDetailsMapper, UserResaleCertificateMapper } from './user-buying-preference-mapper';
import { CompanyBuyNowPayLater, UserAchCredit, UserBuyingPreference, UserDeliveryReceivingAvailabilityDetails, UserResaleCertificate, PaymentInfo, User, UserRequestIncreaseCredits, <PERSON><PERSON>zosLogger, CustomDepositDetails, GlobalDepositDetails } from '@bryzos/extended-widget-library';
import { Constants } from 'src/Constants';
import { AwsQueue } from 'src/AwsQueue';
import { Balance } from 'src/Balance';
import { UserService } from '../user.service';

@Injectable()
export class UserBuyingPreferenceService {

  private maperClass = new UserBuyingPreferenceMapper();
  private dbServiceObj = new DataBaseService()

  constructor(
    private readonly awsQueue:AwsQueue,
    private readonly balance:Balance,
    private readonly userService: UserService,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(UserDeliveryReceivingAvailabilityDetails) private readonly userDeliveryReceivingAvailabilityDetailsRepository: Repository<UserDeliveryReceivingAvailabilityDetails>,
    @InjectRepository(UserAchCredit) private readonly userAchCreditRepository: Repository<UserAchCredit>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserRequestIncreaseCredits) private readonly userRequestIncreaseCreditsRepository: Repository<UserRequestIncreaseCredits>,
    @InjectRepository(CustomDepositDetails) private readonly customDepositDetailsRepository : Repository<CustomDepositDetails>,
    @InjectRepository(GlobalDepositDetails) private readonly globalDepositDetailsRepository : Repository<GlobalDepositDetails>
  ) { }


  async create(createDto: BuyingPreferenceDto, userId: string, superAdminUserId: string) {
    let buyingPreference = createDto;

    if([Constants.PAYMENT_METHOD_BNPL].includes(buyingPreference.default_payment_method)) {
      //check if BNPL is set
      let bnplSettings = await this.dbServiceObj.findByUserId(this.companyBuyNowPayLaterRepository,userId);
      if(bnplSettings === undefined) return { "error_message": "Please setup BNPL payment." };
    }

    if(buyingPreference.client_company === null) {
      return { "error_message": "Please fill your company." }
    }

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

    buyingPreference["company_id"] = getUserData.company_id;
    if(superAdminUserId) {
      buyingPreference["super_admin_user_id"] = superAdminUserId;
    }
    
    //save buying preference
    await this.dbServiceObj.save(buyingPreference, this.maperClass, userId, this.userBuyingPreferenceRepository);

    if(buyingPreference.client_company) {
      this.userService.updateUser({ 'client_company': buyingPreference.client_company, first_name: buyingPreference.first_name, last_name: buyingPreference.last_name }, userId);
    }
    
    if(buyingPreference.hasOwnProperty('resale_certificate') && Array.isArray(buyingPreference.resale_certificate)) {
      let certificate = await this.saveResaleCertificate(userId, buyingPreference.resale_certificate,superAdminUserId);
    }

    if (buyingPreference.hasOwnProperty('user_delivery_receiving_availability_details') && Array.isArray(buyingPreference.user_delivery_receiving_availability_details)) {
      //deactivate all entries for user
      
      let checkIfAlreadyExist = await this.dbServiceObj.findByUserId(this.userDeliveryReceivingAvailabilityDetailsRepository, userId)
      if (checkIfAlreadyExist) {
        await this.dbServiceObj.markInActiveMultipleWhere(this.userDeliveryReceivingAvailabilityDetailsRepository,{'user_id': userId});
      }
      let userAvailableDayArray:any = [];
      for (let dto of buyingPreference.user_delivery_receiving_availability_details) {
        let userAvailableDayObject = {
          "day": dto.day,
          "from": dto.from,
          "to": dto.to,
          "display_name": dto.display_name,
          "is_user_available": dto.is_user_available ? true : false,
          "user_id": userId,
          "super_admin_user_id": superAdminUserId
        }
        userAvailableDayArray.push(userAvailableDayObject);
      };
      await this.dbServiceObj.saveData(userAvailableDayArray,this.userDeliveryReceivingAvailabilityDetailsRepository);
    }

    let response = await this.getBuyingPreferenceData(userId);
    return response;
  }

  async getBuyingPreferenceData(userId: string) {
    let balanceBuyerId = null;
    let buyingPreferenceData = await this.dbServiceObj.findByUserId(this.userBuyingPreferenceRepository, userId);
    
    let resaleCertificate = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository, userId);

    let userDeliveryReceivingAvailabilityDetails = await this.dbServiceObj.findAllAndOrderByWeekDays(this.userDeliveryReceivingAvailabilityDetailsRepository, userId);

    let achCredit = await this.dbServiceObj.findByUserId(this.userAchCreditRepository, userId);

    const checkoutViaBalance = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository,"name",Constants.CHECKOUT_VIA_BALANCE);
    let checkInBalance = checkoutViaBalance.value;

    let bnplSettings = await this.dbServiceObj.findOneWithOrComparision(this.companyBuyNowPayLaterRepository, {'user_id': userId} , 'is_approved', [null, '1']);
    if(bnplSettings != undefined) {
      bnplSettings.balance_credit_limit = 0;
      bnplSettings.balance_available_credit_limit = 0;
      balanceBuyerId = checkInBalance === Constants.ON ? await this.balance.getBuyerId(userId) : null;
    }
    
    if(buyingPreferenceData) {
      buyingPreferenceData.resale_certificate = resaleCertificate;
      buyingPreferenceData.user_delivery_receiving_availability_details = userDeliveryReceivingAvailabilityDetails;
      buyingPreferenceData.ach_credit = achCredit,
      buyingPreferenceData.bnpl_settings = bnplSettings
      
      if(buyingPreferenceData.bnpl_settings){
        buyingPreferenceData.bnpl_settings.outstanding_amount = 0; 
      }

      if(checkInBalance === Constants.ON) {
        if(balanceBuyerId !== null && bnplSettings != undefined) {
          let balanceKey = await this.balance.getBalanceKey();
          let balanceResponse =  await this.balance.getBuyerCreditLimit(balanceBuyerId,balanceKey);
          buyingPreferenceData.bnpl_settings.balance_credit_limit = balanceResponse['balance_credit_limit'];
          buyingPreferenceData.bnpl_settings.balance_available_credit_limit = balanceResponse['balance_available_credit_limit'];
          buyingPreferenceData.bnpl_settings.outstanding_amount = balanceResponse['balance_credit_limit'] - balanceResponse['balance_available_credit_limit'];
        }
      }else{
        if(bnplSettings != undefined) {

          bnplSettings.bryzos_credit_limit = bnplSettings.bryzos_credit_limit ? bnplSettings.bryzos_credit_limit : 0;
          bnplSettings.bryzos_available_credit_limit = bnplSettings.bryzos_available_credit_limit ? bnplSettings.bryzos_available_credit_limit : 0;

          buyingPreferenceData.bnpl_settings.balance_credit_limit = bnplSettings.bryzos_credit_limit;
          buyingPreferenceData.bnpl_settings.balance_available_credit_limit = bnplSettings.bryzos_available_credit_limit;
          buyingPreferenceData.bnpl_settings.outstanding_amount = bnplSettings.bryzos_credit_limit - bnplSettings.bryzos_available_credit_limit;
        }
      }
    } else {
      buyingPreferenceData = {'ach_credit': achCredit, 'user_delivery_receiving_availability_details': userDeliveryReceivingAvailabilityDetails, 'bnpl_settings': bnplSettings};
      
    }
    
    return buyingPreferenceData;
  }

  async keyValueArrayMatches(object1: any, object2: any, keyValueArray: any) {
    return keyValueArray.every(([key, value]) => object1[key] === object2[key] && object1[key] === value);
  }

  async saveResaleCertificate(userId, resaleCertificate,superAdminUserId:string) {
    let resaleCertificateId = null;
    let allCurrentResaleCert = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository, userId);
    
    let notDeletable = allCurrentResaleCert.filter(obj => obj.is_deletable == false);
    const stateIds = notDeletable.map(obj => obj.state_id);
    const filterResaleCertificate = resaleCertificate.filter(obj => !stateIds.includes(obj.state_id));

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId);
    const companyId = getUserData.company_id;

    let existingIds = resaleCertificate.filter(el => el.hasOwnProperty('id')).map(el => el.id).map(String);
    //update existing certificates with given values
    await this.dbServiceObj.markInactiveWithNotInCondition(this.userResaleCertificateRepository, userId, 'id', existingIds);
    for(let certificate of filterResaleCertificate) {

      //deactivate if same state_id certificate exists 
      await this.dbServiceObj.markInActiveMultipleWhere(this.userResaleCertificateRepository, {'user_id': userId, 'state_id': certificate.state_id});

      let existingCert = await allCurrentResaleCert.find(cert => cert.id === certificate.id);
      
      let keyValueArray = [
        ['state_id', certificate.state_id], 
        ['cerificate_url_s3', certificate.cerificate_url_s3], 
        ['expiration_date', certificate.expiration_date]
      ];

      //check if user has changed any above parameter for existings certificate
      let match = false;
      if(existingCert != undefined) {
        match = await this.keyValueArrayMatches(existingCert, certificate, keyValueArray);
        if(!match) {
          certificate.status = Constants.SALES_TAX_EXEMPTION_STATUS_PENDING;
          await this.dbServiceObj.markInActive('id', existingCert.id, this.userResaleCertificateRepository);
        } else {
          //reactivate in user_resale_certificate but don't send email
          resaleCertificateId = await this.dbServiceObj.updateByColumnId(this.userResaleCertificateRepository, {'is_active': true,'super_admin_user_id' : superAdminUserId}, 'id', existingCert.id);
        }
      }

      if(certificate.status == undefined) certificate.status = Constants.SALES_TAX_EXEMPTION_STATUS_PENDING;
      else certificate.status = certificate.status;
      
      if(!match) {
        certificate["company_id"] = companyId;
        certificate["super_admin_user_id"] =superAdminUserId;
        resaleCertificateId = await this.dbServiceObj.save(certificate, new UserResaleCertificateMapper(), userId, this.userResaleCertificateRepository,true);
        await this.awsQueue.sendSalesTaxApproval(resaleCertificateId);
      }
    }
    return resaleCertificateId;
  }

  async saveBuyerProfile(buyerProfileDto: BuyerProfileDto, userId: string, superAdminUserId: string) {
    let response = null;
    let buyerProfile = buyerProfileDto;
    buyerProfile["super_admin_user_id"] = superAdminUserId;
    const userUpdateDto = { first_name: buyerProfile.first_name, last_name: buyerProfile.last_name };
    await this.userService.updateUser(userUpdateDto, userId);

    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerProfile, 'user_id', userId, this.userBuyingPreferenceRepository);
    
    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }

    return response;
  }

  async saveBuyerCompanyInfo(buyerCompanyDto: BuyerCompanyInfoDto, userId: string, superAdminUserId: string) {
    let response=null;
    let buyerCompanyInfo = buyerCompanyDto;
    if(buyerCompanyInfo.client_company === null ) {
      return { "error_message": "Please fill your company." }
    }

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

    buyerCompanyInfo["company_id"] = getUserData.company_id;
    buyerCompanyInfo["super_admin_user_id"] = superAdminUserId;
    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerCompanyInfo, 'user_id', userId, this.userBuyingPreferenceRepository);
    if(buyerCompanyInfo.client_company) {
      this.userService.updateUser({'client_company': buyerCompanyInfo.client_company}, userId);
    }
    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }
    return response;
  }

  async saveBuyerDeliverAddInfo(buyerDeliverDto: BuyerDeliveryDto, userId: string, superAdminUserId: string) {
    let response = null;

    let buyerDeliveryInfo = buyerDeliverDto;
    buyerDeliveryInfo["super_admin_user_id"] = superAdminUserId;
    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerDeliveryInfo, 'user_id', userId, this.userBuyingPreferenceRepository);

    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }

    return response; 
  }
  
  async saveBnplRequest(userId: string, payloadData: BnplRequestDto, superAdminUserId: string) {
    (payloadData.agreed_terms as any) === 1 ? payloadData.agreed_terms=true : (payloadData.agreed_terms as any) === 0 ? payloadData.agreed_terms=false : payloadData.agreed_terms;
    let response = null;
    //soft delete existing payment id
    await this.dbServiceObj.markInActiveMultipleWhere(this.paymentInfoRepository, {'user_id': userId, 'pgpm_mapping_id': payloadData.pgpm_mapping_id});
    let paymentInfoId = await this.dbServiceObj.saveWithOutMapper({'user_id': userId, 'pgpm_mapping_id': payloadData.pgpm_mapping_id}, userId, this.paymentInfoRepository);

    //soft delete existing BNPL request
    await this.dbServiceObj.markInActiveMultipleWhere(this.companyBuyNowPayLaterRepository, {'user_id': userId});
    let responseId = await this.dbServiceObj.saveWithOutMapper({'ein_number': payloadData.ein_number, 'duns': payloadData.duns_number, 'requested_credit_limit': payloadData.desired_credit_limit, 'payment_info_id': paymentInfoId, 'reference_document_id': payloadData.reference_document_id,'agreed_terms':payloadData.agreed_terms, 'super_admin_user_id': superAdminUserId}, userId, this.companyBuyNowPayLaterRepository);

    if(responseId){
      let buyerSettingData = await this.dbServiceObj.findOne(this.userBuyingPreferenceRepository,'user_id', userId)
      let dto ={"bnpl_email": buyerSettingData.email_id}
      this.dbServiceObj.updateWithoutMapper(dto, 'id', userId, this.userRepository)
      //send admin BNPL approval request email
      this.awsQueue.sendDataToAwsQueue(responseId, 'WIDGET_BRYZOS_BUCK_APPROVAL_REQUEST', 'New Register for Bryzos Approval!', process.env.MESSAGE_NODE_EMAIL_QUEUE);
    }
    response = 'Request saved successfully';
    return response;
  }

  async saveDefaultPaymentMethod(userId: string, payload:any, superAdminUserId: string) {
    let response = null;
    if(payload.hasOwnProperty('default_payment_method') && payload.default_payment_method != '') {
      await this.dbServiceObj.saveOrUpdateWithOutMapper({"default_payment_method": payload.default_payment_method,'super_admin_user_id': superAdminUserId}, userId, this.userBuyingPreferenceRepository);
      response = 'Saved successfully';
    }
    else {
      response= { "error_message": "Payment method not found" };
    }
    return response;
  }

  async saveReceivingHrsInfo(buyerReceivingHrsDto: DeliveryAvailibilityDto[], userId: string, superAdminUserId: string) {
    let response = null;
    let updateData = null;
    let buyerReceivingHrs = buyerReceivingHrsDto;
  
    //deactivate all entries for user
    await this.dbServiceObj.markInActiveMultipleWhere(this.userDeliveryReceivingAvailabilityDetailsRepository,{'user_id': userId});

    let userAvailableDayArray:any = [];
    for (let buyerReceivingHr of buyerReceivingHrs) {
      let userAvailableDayObject = {
        "day": buyerReceivingHr.day,
        "from": buyerReceivingHr.from,
        "to": buyerReceivingHr.to,
        "display_name": buyerReceivingHr.display_name,
        "is_user_available": buyerReceivingHr.is_user_available ? true : false,
        "user_id": userId,
        'super_admin_user_id': superAdminUserId
      }
      userAvailableDayArray.push(userAvailableDayObject);
    };
    updateData = await this.dbServiceObj.saveData(userAvailableDayArray,this.userDeliveryReceivingAvailabilityDetailsRepository);
    
    if(updateData)
      response = "Saved Successfully"
    else
      response= { "error_message": "Something went wrong!" };
    
    return response;
  }

  async saveDocumentLibrary(buyerDocumentLibDto: ResaleCertificateDto, userId: string,superAdminUserId: string) {
    let response = null;
    let certificate = null;
    let buyerDucumentLib = buyerDocumentLibDto;

    certificate = await this.saveResaleCertificate(userId, buyerDucumentLib,superAdminUserId);
    
    if(certificate)
      response = "Saved Successfully"
    else
      response= { "error_message": "Something went wrong!" };
    
    return response;
  }

  async increaseCreditLimitRequest( payloadData: IncreaseCreditLimitDto, userId: string, superAdminUserId: string){
    let response = null;
    let cbnplData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{'user_id':userId,'is_active':1,'is_approved':1})
    if(!cbnplData){
      return { error_message: "Something went wrong!" };
    }
    else if(cbnplData && cbnplData.credit_status == Constants.REQUESTED_CREDIT_PENDING_INCREASE){
      return { error_message: "Already requested" };
    }
    await this.dbServiceObj.markInActiveMultipleWhere(this.userRequestIncreaseCreditsRepository, {'user_id': userId});
    let responseId = await this.dbServiceObj.saveWithOutMapper({'requested_credit':payloadData.request_increase_credit,'status':Constants.REQUESTED_INCREASE_CREDIT_PENDING, 'super_admin_user_id': superAdminUserId }, userId, this.userRequestIncreaseCreditsRepository);
    if(responseId){
      await this.dbServiceObj.updateWithoutMapper({'user_request_increase_credit_id':responseId,'requested_increase_credit':payloadData.request_increase_credit,'credit_status':Constants.REQUESTED_CREDIT_PENDING_INCREASE,'is_requested_increase_credit_approved':null},'user_id',userId,this.companyBuyNowPayLaterRepository);
      try{
        this.awsQueue.sendDataToAwsQueue(responseId, 'INCREASE_BRYZOS_BUCK_APPROVAL_REQUEST', 'MessageBody Request to Extend Bryzos Buck Credits!', process.env.MESSAGE_NODE_EMAIL_QUEUE);  
        response = "Increase Credit Request Received";
      }
      catch(error){
        BryzosLogger.log(JSON.stringify({ error: error, event: "Increase Credit Request" }), process.env.LOGGLY_ERROR_TAG);
        return { error_message: "Something went wrong" };
      }
    }
    return response;
  }

  async cancelIncreaseCreditLimitRequest(userId: string, superAdminUserId: string){
    let response = null;
    let userRequestedCreditData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{'user_id':userId,'is_active':1,'is_approved':1})
    
    if(userRequestedCreditData && userRequestedCreditData.requested_increase_credit && userRequestedCreditData.credit_status == Constants.REQUESTED_CREDIT_PENDING_INCREASE){
      await this.dbServiceObj.updateWithoutMapper({'status':Constants.REQUESTED_INCREASE_CREDIT_CANCELLED,'action_performed_by':userId, 'super_admin_user_id': superAdminUserId},'user_id',userId,this.userRequestIncreaseCreditsRepository);
      await this.dbServiceObj.updateWithoutMapper({'is_requested_increase_credit_approved':false,'credit_status':Constants.REQUESTED_INCREASE_CREDIT_CANCELLED},'user_id',userId,this.companyBuyNowPayLaterRepository);

      response = "Cancel Request Received";
    }
    else{
      return { error_message: "Invalid user request!" };
    }
    return response;
  }

  async getDepositAmount(payload:depositAmountDto, userId:string){
    let depositData={};
    let depositPercentage=0.00;
    let depositAmount=0.00;
    if(payload.payment_method.toUpperCase() != Constants.PAYMENT_METHOD_ACH_CREDIT){
      return;
    }
    const customDepositData = await this.dbServiceObj.findOneByMultipleWhere(this.customDepositDetailsRepository,{'user_id':userId})
    if(customDepositData){
      depositPercentage = customDepositData.deposit_percentage;
    }
    else{
      const globalDepositData = await this.dbServiceObj.findOneDescOrder(this.globalDepositDetailsRepository)
      if(globalDepositData){
        depositPercentage = globalDepositData.deposit_percentage;
      }
    }
    
    if(depositPercentage > 0){
      depositAmount = payload.price * depositPercentage/100
    }
    depositData['depositPercentage']= depositPercentage;
    depositData['deposit_amount'] = depositAmount;
    return depositData;
  }
}
