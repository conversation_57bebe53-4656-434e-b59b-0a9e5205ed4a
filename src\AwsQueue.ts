import { AwsUtilityV3, BaseLibraryService, DataBaseService } from "@bryzos/base-library";
import { Inject, Injectable } from "@nestjs/common";
import { Constants } from "./Constants";
import { <PERSON><PERSON><PERSON><PERSON>Logger } from "@bryzos/extended-widget-library";
import { InjectRepository } from "@nestjs/typeorm";
import { UserDeleteAccount } from "@bryzos/extended-widget-library";
import { Repository } from "typeorm";
@Injectable()
export class AwsQueue {
    private dbServiceObj = new DataBaseService()

    constructor(private readonly awsUtility:AwsUtilityV3,

        @Inject(BaseLibraryService) private readonly baseLibraryService : BaseLibraryService,
        @InjectRepository(UserDeleteAccount) private readonly userDeleteAccountRepository: Repository<UserDeleteAccount>,


    ) {}

    async sendShareWidgetEmail(id:string) {
        let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
        let messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: id
            },
            "event": {
                DataType: "String",
                StringValue: "WIDGET_SERVICE_APP_SHARE_REQUEST"
            }
        };
        let  messageBody= "Send widget service share request";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendShareProductPricing(id:string) {
       let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE; 
        let messageAttributes= {
                "reference_id": {
                    DataType: "String",
                    StringValue: id
                },
                "event": {
                    DataType: "String",
                    StringValue: "WIDGET_SERVICE_SHARE_PRODUCT_PRICING"
                }
            };
        let messageBody= "Share product pricing";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)

    }

    async sendSalesTaxApproval(id:string) {
        let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
        let messageAttributes= {
                "reference_id": {
                    DataType: "String",
                    StringValue: id
                },
                "event": {
                    DataType: "String",
                    StringValue: "WIDGET_SERVICE_SALES_TAX_APPROVAL"
                }
            };
        let messageBody= "Send sales tax approval email";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendSellerPaymentSetup(sellerPaymentId, authorizedForSuperEmail = "false") {
        let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
        let messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: sellerPaymentId
            },
            "authorized_for_error_email": {
                DataType: "String",
                StringValue: authorizedForSuperEmail
            },
            "event": {
                DataType: "String",
                StringValue: "WIDGET_SERVICE_SELLER_PAYMENT"
            }
        }
        let messageBody= "Send seller payment setup email";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendDataToAwsQueue(referenceId: string, eventName: string, sqsMessageBody: string, queueURL?: string | null) {

        if (!queueURL) {
            queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
        }

        let messageAttributes = {
            "reference_id": {
                DataType: "String",
                StringValue: referenceId
            },
            "event": {
                DataType: "String",
                StringValue: eventName
            }
        };
        let messageBody = sqsMessageBody;
        this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
    }

    async sendDeleteResaleCertAlert(certId)
    {
        const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;

        const messageAttributes= {
            "cert_id": {
                DataType: "String",
                StringValue: certId
            },
            "event": {
                DataType: "String",
                StringValue: "USER_DELETE_RESALE_CERTIFICATE"
            }
        };
        const messageBody= "Delete resale certificate id";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendDeleteResaleCertNotification(buyerId)
    {
        const queueURL = process.env.NOTIFICATION_QUEUE_URL;

        const messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: buyerId
            },
            "event": {
                DataType: "String",
                StringValue: "NOTIFICATION_BUYER_SALES_TAX_REMINDER"
            }
        };
        const messageBody= "Delete resale certificate notification";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }



    async globalSignOutUser(user)
    {

        let response = null;

        let cognitoUserName = user.cognito_user_name;
        let email_id = user.email_id;

        const insert = {
            user_id: user.id,
            status: null
          };

        try {
            
            const deleteData = await this.awsUtility.userGlobalSignOut(cognitoUserName); 
            if(deleteData){
                BryzosLogger.log(JSON.stringify({"response": {"cognito_result":deleteData, "emailId": email_id,'event': 'Global Signout'}}), process.env.LOGGLY_DELETE_USER_TAG);
                response = "User Deleted successfully";
            }else{
                BryzosLogger.log(JSON.stringify({"cognito_result":deleteData, "emailId": email_id,'event': 'Global Signout'}), process.env.LOGGLY_DELETE_USER_TAG);
                response = "Something went wrong";
            }

        } catch (error) {
            
            BryzosLogger.log(JSON.stringify({"errorMessage": JSON.stringify(error.toString()), "emailId": email_id,'event': 'Global Signout'}), process.env.LOGGLY_DELETE_USER_TAG);
            response = "Something went wrong";
        }
        return response;

    }

    async sendReferenceDataProductUpdateEmail(version)
    {
        const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;

        const messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: version
            },
            "event": {
                DataType: "String",
                StringValue: "UPDATE_REFERENCE_DATA_PROODUCT_EMAIL"
            }
        };
        const messageBody= "Upadted Reference Data Product Email";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendShareVideoToEmailId(referenceId:string) {
        const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
        const messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: referenceId
            },
            "event": {
                DataType: "String",
                StringValue: "SHARE_VIDEO"
            }
        };
        const messageBody= "share video to email-id";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendOpenAiErrorResponseEmail(referenceId:string){
        const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;

        const messageAttributes= {
            "reference_id": {
                DataType: "String",
                    StringValue: referenceId
            },
            "event": {
                DataType: "String",
                StringValue: "OPEN_AI_ERROR_EMAIL"
            }
        };
        const messageBody = "Open Ai Error Response Email";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendIdtoFetchBuyerPoNumber(referenceId:string)
    {
        const queueURL = process.env.SELLER_INVOICE_READ_QUEUE;

        const messageAttributes= {
            "reference_id": {
                DataType: "String",
                StringValue: referenceId
            },
            "event": {
                DataType: "String",
                StringValue: "FETCH_PO_NUMBER_FROM_ORDER"
            }
        };
        const messageBody = "send reference id ot fetch buyer po number from order tables";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }

    async sendNewUserSignUpEmail(userId)
    {
        const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;

        const messageAttributes = {
            "reference_id": {
                DataType: "String",
                StringValue: userId
            },
            "event": {
                DataType: "String",
                StringValue: "NEW_USER_SIGNUP"
            }
        };
        const messageBody = "New User Signup Email";
        this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
    }
    
    async errorMessage( message: string, referenceId: string, subject: string, authorizedForSuperEmail = 'false', ) {
      const queueUrl = process.env.SQS_QUEUE_URL;
      const messageBody = 'Message:Errors';
      const messageAttributes = {
        reference_id: { DataType: 'String', StringValue: referenceId },
        result: { DataType: 'String', StringValue: message },
        subject: { DataType: 'String', StringValue: subject },
        authorized_for_error_email: { DataType: 'String', StringValue: authorizedForSuperEmail, },
        event: { DataType: 'String', StringValue: 'ERROR_MESSAGE' },
      };
  
      try {
        this.awsUtility.sendDataToSQS(queueUrl, messageAttributes, messageBody);
      } catch (error) {
        console.log(error);
      }
    }
}