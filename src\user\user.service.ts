import { AwsUtilityV3, DataBaseService, BaseLibraryService, ReferenceDataSettings, Constants as LibConstants, CognitoAuthService, Utility as BaseLibUtility } from '@bryzos/base-library';
import { CompanyBuyNowPayLater, ExtendedWidgetVideoLibrary, PaymentInfo, ReferenceDataSalesTax, UserArPaymentInfo, UserBuyingPreference, UserDeleteAccount, UserOrderRatings, UserProductTagMapping, UserPurchaseOrder, UserSellingPreference, SignUpPreApprovedEmail, UserGameScore, Constants as ExtLibConstants, SaveOrderDraftLines, SaveOrderDraft, AwsTextractService } from '@bryzos/extended-widget-library';
import { ReferenceDataBryzosTermsConditions, User, UserLogger, UserPricingFeedback, UserShareProductPricing, UserShareWidgetRequest, WidgetTermsCondtionUserActions, UserResaleCertificate,ReferenceDataOrderStatus, UserCreatepoOpenClose, UserSearchAnalytics, UserOnboradPendingRequests,UserSearchLineDetails,ReferenceDataProductsWidget,UserPurchaseOrderLine, ReferenceDataStateZipcode, UserMainCompany, UserPendingCompanyRequests,UserSearchPriceDetails,UserSearchPriceScreenMoveOut, ChatUsers, VideoLibraryUserViewLog, ShareVideoAnalytics, SignupUtility, UserBomUploads, UserBomProductExtract, BomProductExtractionUpdateLogs,Utility as ExtLibUtility, OrderUtilityLibrary } from '@bryzos/extended-widget-library';
import { Injectable, BadRequestException, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { AwsQueue } from 'src/AwsQueue';
import { Constants } from 'src/Constants';
import { PORatingsDto } from 'src/purchase-order/dto/save-po-ratings.dto';
import { Repository, Connection } from 'typeorm';
import { GetSignedUrl, PoS3UrlsDto, PricingFeedbackDto, ShareProductPricing, ShareWidgetRequest, UserLoginDto, WidgetTermsCondtionUserActionsDto, OnBoardDto, UserEmailDto, TooltipDto, ForegroundBackgroundActivityDto, VideoViewDto, ShareVideoAnalyticsDto, CreateUserDto, MigrateToPasswordLessDto, GlobalSignoutDto, BomUpload, SelectedBomProducts, GameScore, BomDraftDto, BomSummaryDto, BomHeaderDetailsDto, BomUploadResultDto } from './dto/user.dto';

import { Workbook } from 'exceljs';
import * as tmp from 'tmp';
// import { resolve } from 'path';
import { BryzosLogger } from "@bryzos/extended-widget-library";

import { Utility } from 'src/utility';
import { PdfMakerService } from '@bryzos/pdf-maker';
// import { ChannelAddUsersDto, ChatRoomDto } from './dto/chat.dto';
// import { CreateUserDto } from './dto/create-user.dto';
const axios = require('axios');
import ShortUniqueId from 'short-unique-id';
import * as fs from 'fs-extra';
import { json } from 'stream/consumers';
@Injectable()
export class UserService {
  baseUrl = process.env.DEAD_SIMPLE_CHAT_BASE_URL;
  params = { auth: process.env.DEAD_SIMPLE_CHAT_PRIVATE_KEY };
  roomId = process.env.DEAD_SIMPLE_CHAT_ROOM_ID;
  private uniqueId = new ShortUniqueId();

  private dbServiceObj = new DataBaseService();
  private readonly baseLibUtility = new BaseLibUtility();
  constructor(private readonly awsQueue:AwsQueue,
    private readonly awstextract: AwsTextractService,
    private readonly connection: Connection,
    private readonly baseLibraryService: BaseLibraryService,
    private readonly pdfMakerService: PdfMakerService,
    private readonly cognitoAuthService:CognitoAuthService,
    private signupUtility : SignupUtility,
    private readonly extLibUtility : ExtLibUtility,
    private readonly orderUtilityLibrary: OrderUtilityLibrary,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserPricingFeedback) private readonly pricingFeedback: Repository<UserPricingFeedback>,
    @InjectRepository(UserShareWidgetRequest) private readonly shareWidgetRequest: Repository<UserShareWidgetRequest>,
    @InjectRepository(UserLogger) private readonly userLogger: Repository<UserLogger>,
    @InjectRepository(UserShareProductPricing) private readonly userShareProductPricing: Repository<UserShareProductPricing>,
    @InjectRepository(ReferenceDataBryzosTermsConditions) private readonly refBryzosTermsCondition: Repository<ReferenceDataBryzosTermsConditions>,
    @InjectRepository(WidgetTermsCondtionUserActions) private readonly widgetTermsConditionRepository: Repository<WidgetTermsCondtionUserActions>,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserOrderRatings) private readonly userOrderRatingsRepository: Repository<UserOrderRatings>,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(UserProductTagMapping) private readonly userProductTagMappingRepository: Repository<UserProductTagMapping>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatus: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(UserCreatepoOpenClose) private readonly userCreatepoOpenClose: Repository<UserCreatepoOpenClose>,
    @InjectRepository(UserSearchAnalytics) private readonly userSearchAnalytics: Repository<UserSearchAnalytics>,
    @InjectRepository(UserOnboradPendingRequests) private readonly userOnboradPendingRequestsRepository: Repository<UserOnboradPendingRequests>,
    @InjectRepository(UserSearchLineDetails) private readonly userSearchLineDetailsRepository: Repository<UserSearchLineDetails>,
    @InjectRepository(ReferenceDataProductsWidget) private readonly referenceDataProductsWidgetRepository: Repository<ReferenceDataProductsWidget>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(ReferenceDataStateZipcode) private readonly referenceDataStateZipcodeRepository: Repository<ReferenceDataStateZipcode>,
    @InjectRepository(UserMainCompany) private readonly userMainCompanyRepository: Repository<UserMainCompany>,
    @InjectRepository(UserPendingCompanyRequests) private readonly userPendingCompanyRequestsRepository: Repository<UserPendingCompanyRequests>,
    @InjectRepository(UserSearchPriceDetails) private readonly userSearchPriceDetailsRepository: Repository<UserSearchPriceDetails>,
    @InjectRepository(UserSearchPriceScreenMoveOut) private readonly userSearchPriceScreenMoveOutRepository: Repository<UserSearchPriceScreenMoveOut>,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
    @InjectRepository(UserSellingPreference) private readonly userSellingPreferenceRepository: Repository<UserSellingPreference>,
    @InjectRepository(UserDeleteAccount) private readonly userDeleteAccountRepository: Repository<UserDeleteAccount>,
    @InjectRepository(ChatUsers) private readonly chatUsersRepository: Repository<ChatUsers>,
    @InjectRepository(ReferenceDataSalesTax) private readonly referenceDataSalesTaxRepository: Repository<ReferenceDataSalesTax>,
    @InjectRepository(ExtendedWidgetVideoLibrary) private readonly extendedWidgetVideoLibraryRepository: Repository<ExtendedWidgetVideoLibrary>,
    @InjectRepository(VideoLibraryUserViewLog) private readonly videoLibraryUserViewLogRepository: Repository<VideoLibraryUserViewLog>,
    @InjectRepository(ShareVideoAnalytics) private readonly shareVideoAnalyticsRepository: Repository<ShareVideoAnalytics>,
    @InjectRepository(SignUpPreApprovedEmail) private readonly signUpPreApprovedEmailRepository: Repository<SignUpPreApprovedEmail>,
    @InjectRepository(UserBomUploads) private readonly userBomUploadsRepository: Repository<UserBomUploads>,
    @InjectRepository(UserBomProductExtract) private readonly userBomProductExtractRepository: Repository<UserBomProductExtract>,
    @InjectRepository(BomProductExtractionUpdateLogs) private readonly bomProductExtractionUpdateLogsRepository: Repository<BomProductExtractionUpdateLogs>,
    @InjectRepository(UserGameScore) private readonly userGameScoreRepository: Repository<UserGameScore>,
    @InjectRepository(SaveOrderDraftLines) private readonly saveOrderDraftLinesRepository: Repository<SaveOrderDraftLines>,
    @InjectRepository(SaveOrderDraft) private readonly saveOrderDraftRepository: Repository<SaveOrderDraft>,
    //
    private readonly awsUtility:AwsUtilityV3
  ) {}

  async login(userId, loginDto: UserLoginDto, superAdminUserId: string) {

    let response = null;
    const deviceId = loginDto.device_id;
  
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);

    if(user !== undefined) {
            
       response = {
        ...user,
        disc_is_discounted: user.is_buyer_spread,
        disc_discount_pricing_column: user.base_pricing_column,
        disc_discount_rate: user.buyer_spread_rate,
        disc_is_discount_var_overriden: user.is_buyer_spread_overriden,
        disc_discount_period: user.deprecated_disc_discount_period,
        disc_discount_phaseout_startdate: user.deprecated_disc_discount_phaseout_startdate,
        disc_discount_phaseout_period: user.deprecated_disc_discount_phaseout_period,

      };

      //get current tnc version
      let tncVersion = await this.dbServiceObj.findOneByMultipleWhere(this.refBryzosTermsCondition,{type:user.type});
      if(tncVersion != undefined) {
        response.current_tnc_version = tncVersion.terms_conditions_version;
      }

      // get chat_user data
      const chatUserData = await this.dbServiceObj.findOneByMultipleWhere(this.chatUsersRepository, { external_user_id: user.id });
      if (chatUserData) {
        const isModerator = !!chatUserData.is_moderator;
        response['chat_data'] = {
          unique_user_identifier: chatUserData.unique_user_identifier,
          access_token: isModerator ? chatUserData.access_token : null,
          is_moderator: isModerator,
          user_id: chatUserData.id,
        }
      }

      //update last login for this user
      let currentDate = new Date();
      const utcDate = utcToZonedTime(currentDate, 'UTC');
      let lastLogin = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

      const mappedDeviceIds = JSON.parse(user.mapped_device_ids ?? '[]');
      if (deviceId) {
        if (!mappedDeviceIds.find(id => id === deviceId)) {
          mappedDeviceIds.push(deviceId);
        }
      }

      await this.dbServiceObj.updateWithoutMapper({ 'last_login': lastLogin, "os_version": loginDto.os_version, "last_login_app_version":loginDto.last_login_app_version, mapped_device_ids: JSON.stringify(mappedDeviceIds), 'is_cognito_confirmed': true }, 'id', userId, this.userRepository);
      await this.dbServiceObj.saveWithOutMapper( { "event": Constants.USER_LOGIN, "email_id": loginDto.email_id, "zip_code": loginDto.zip_code,  "os_version": loginDto.os_version, "ui_version": loginDto.ui_version, "last_login_app_version":loginDto.last_login_app_version, device_id: deviceId, super_admin_user_id: superAdminUserId }, userId, this.userLogger);
    } else {
      return { "error_message": "User not found" };
    }
    return response;
  }

  async findOne(id) {
    let response = null
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', id);
    if(user !== undefined) {
      response = user
    } else {
      user = null;
    }
		return response;
  }

  async savePricingFeedback(userId, pricingFeedbackDto: PricingFeedbackDto) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    pricingFeedbackDto.user_type = user.type;
    let feedback = await this.dbServiceObj.saveOrUpdateByMultipleWhere(pricingFeedbackDto, this.pricingFeedback, {'product_id':pricingFeedbackDto.product_id, 'price_feedback_type': pricingFeedbackDto.price_feedback_type, 'user_type': user.type, 'user_id': userId});
    if(feedback) {
      response = "Feedback noted";
    }
    return response;
  }

  async saveShareWidgetRequest (userId, shareWidetRequestDto: ShareWidgetRequest, superAdminUserId: string) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    if(shareWidetRequestDto.to_email == shareWidetRequestDto.from_email) return { "error_message": "To and from email cannot be same" };
    shareWidetRequestDto["super_admin_user_id"]= superAdminUserId;
    let shareWidgetRequest = await this.dbServiceObj.saveWithOutMapper(shareWidetRequestDto, shareWidetRequestDto.user_id,this.shareWidgetRequest);
    if(shareWidgetRequest) {
      await this.awsQueue.sendShareWidgetEmail(shareWidgetRequest);
      response = 'Invitation sent successfully'
    }
    return response;
  }

  async logout(userId, loginDto: UserLoginDto, superAdminUserId: string) {
    let response = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);

    if(user !== undefined) {
      let logoutResponse = await this.dbServiceObj.saveWithOutMapper( { "email_id": user.email_id, "event": Constants.USER_LOGOUT,  "os_version": loginDto.os_version, "ui_version": loginDto.ui_version, "last_login_app_version":loginDto.last_login_app_version, "super_admin_user_id": superAdminUserId  }, user.id, this.userLogger);
      if(logoutResponse)
      {
        response = "Logged out successfully!";
      }
    }
    return response;
  }

  async shareProductPricing (userId, shareProductPricingDto: ShareProductPricing, superAdminUserId: string) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    if(shareProductPricingDto.to_email == shareProductPricingDto.from_email) return { "error_message": "To and from email cannot be same" };
    //TODO : Need to modify error messgae
    // if(shareProductPricingDto.price_ft == undefined && shareProductPricingDto.price_lb == undefined) return { "error_message": "No price found" };
    shareProductPricingDto.from_user_type = user.type;
    shareProductPricingDto["super_admin_user_id"] = superAdminUserId;
    let shareProductPricing = await this.dbServiceObj.saveWithOutMapper(shareProductPricingDto, shareProductPricingDto.user_id,this.userShareProductPricing);
    if(shareProductPricing) {
      await this.awsQueue.sendShareProductPricing(shareProductPricing);
      response = 'Product prices shared successfully'
    }
    return response;
  }

  async saveAcceptedTermsConditions(userId, saveWidgetTermsConditionDto: WidgetTermsCondtionUserActionsDto) {
    let response = null;

    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(user) {
      await this.dbServiceObj.saveWithOutMapper(saveWidgetTermsConditionDto, user.id, this.widgetTermsConditionRepository);

      await this.dbServiceObj.updateWithoutMapper({ 'accepted_terms_and_condition': saveWidgetTermsConditionDto.terms_conditions_version}, 'id', user.id, this.userRepository);

      response = 'Saved terms and condition';
    }
    return response;
  }

  
  async getSignedS3Url(userId: string,dto:GetSignedUrl) {
    
   let data = await this.awsUtility.getSignedS3Url(dto.object_key,dto.bucket_name,dto.expire_time);
   return data;
  }

  async saveuploadPoS3Urls(userId, poS3UrlDto: PoS3UrlsDto) {
    let response = null;
    let updateResponse = null;
    //check if PO belongs to user
    let poData = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':poS3UrlDto.po_number, 'buyer_id' : userId});
    if(poData !== undefined && poData.buyer_id == userId) {
      updateResponse = await this.dbServiceObj.updateByMultipleWhere({'upload_po_s3_urls': poS3UrlDto.upload_po_s3_urls}, {'buyer_po_number': poS3UrlDto.po_number, 'buyer_id': userId}, this.userPurchaseOrderRepository);
    }
    
    if(updateResponse) 
      response = 'Saved successfully';
    else 
      response = null;

    return response;
  }

  async savePORatings(userId,savePoRatingDto: PORatingsDto) {
    let response = null;
    let poOrder = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':savePoRatingDto.po_number});
    let canRateOrder = false;
    let saveResult = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(poOrder !== undefined && user !== undefined) {
      if(savePoRatingDto.user_type == Constants.SELLER) {
        canRateOrder = poOrder.claimed_by == user.email_id;
      } else if(savePoRatingDto.user_type == Constants.BUYER) {
        canRateOrder = poOrder.buyer_id == user.id;
      }
    }

    if(canRateOrder) {
      //deactivate existing rating
      await this.dbServiceObj.markInActiveMultipleWhere(this.userOrderRatingsRepository,{'user_id': userId, 'po_number': savePoRatingDto.po_number, 'user_type': savePoRatingDto.user_type});
      saveResult = await this.dbServiceObj.saveWithOutMapper(savePoRatingDto, userId, this.userOrderRatingsRepository);
    }
    
    if(saveResult)
      response = 'Ratings saved successfully'
    else 
      response = null;
    return response;
  }

  async saveUploadSoS3Urls(userId, poS3UrlDto: PoS3UrlsDto) {
    let response = null;
    let updateResponse = null;
    //check if PO belongs to user
    let poOrder = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':poS3UrlDto.po_number, 'seller_id' :userId});
    if(poOrder !== undefined ) {
      updateResponse = await this.dbServiceObj.updateByMultipleWhere({'upload_so_s3_urls': poS3UrlDto.upload_po_s3_urls}, {'buyer_po_number': poS3UrlDto.po_number}, this.userPurchaseOrderRepository);
    }
    
    if(updateResponse) 
      response = 'Saved successfully';
    else 
      response = null;

    return response;
  }

  // async generateExcelFile() {

  //     const data = await this.dbServiceObj.findAll(this.userRepository);

  //     let rows = []

  //       data.forEach(doc => {
  //         rows.push(Object.values(doc))
  //       })
        
  //       let book = new Workbook();
        
  //       let sheet = book.addWorksheet(`sheet1`)
        
  //       rows.unshift(Object.keys(data[0]))
        
  //       sheet.addRows(rows)
        
        
        
  //       let file = await new Promise((resolve, reject) => {
  //         tmp.file({ discardDescriptor: true, prefix : `MyExcelSheet`, postfix: '.xlsx', mode: parseInt('0600', 8)}, async(err, file) => {
  //         if(err){
  //           throw new BadRequestException(err);
  //         }

  //         book.xlsx.writeFile(file).then(aa => {
  //           resolve(file)
  //         }).catch(err =>{
  //           throw new BadRequestException(err);
  //         })
  //       })
  //     })
  //    return file;
  // }

  async generatePurchaseOrderExcelFile(userId: string) {

    const data = await this.connection.query("select upol.domestic_material_only, upol.order_status_id as status, DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date' ,upoled.`transaction_type` as 'Event' ,upo.`payment_method` as 'Method of Payment' ,'BRYZOS' as 'Purchased From', upo.`claimed_by` as 'Fulfilled By' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Shipment Date' ,upo.`buyer_po_number` as 'Bryzos SO' ,upo.`buyer_internal_po` as 'Your PO',upol.`po_line`as 'Line',upol.`description` as 'Description',upol.`actual_qty` as 'Qty',upol.`qty_unit` as 'Qty UM',upol.`buyer_price_per_unit` as 'Price',upol.`price_unit` as 'Price UM' ,upol.`buyer_line_total` as 'Extended',IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) as 'Sales Tax',IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Bryzos Fees',upol.`actual_buyer_line_total` as 'Original total', upo.`buyer_invoice_name` as 'Invoice',upol.`buyer_price_per_unit` as 'Credit/Debit' , upol.`actual_buyer_line_total` + IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) + IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Current Total', upo.`buyer_checkout_pdf_url` as 'URL To Download' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) inner join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`buyer_id` = "+userId+"  Group by upoled.purchase_order_line_id ORDER BY upoled.created_date DESC;" );

    if(data.length > 0){
      
      let rows = []
      const activeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERACTIVE);
      const canceledOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);
      const closeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCOMPLETED);
   
      for(let doc of data)
        {

        if(doc.domestic_material_only == 1) {
          doc.Description = doc.Description + '\n'+Constants.DOMESTIC_MATERIAL_VALUE;
        }
        if(doc.status == activeOrder.id){
          doc.status = Constants.ORDER_STATUS_OPEN;
        }else if(doc.status == canceledOrder.id){
          doc.status = Constants.ORDER_STATUS_CANCELED;
        }else if(doc.status == closeOrder.id){
          doc.status = Constants.ORDER_STATUS_CLOSE;
        }

        let prc = doc.Price; 
     
       let extnd = doc.Extended; 
        let orgTot = doc['Original total']; 
        let currntTot = doc['Current Total'];
        const unit = doc['Price UM'];
        let credDeb = doc['Credit/Debit']; 

        if(unit == Constants.LB)
        {
          prc = await this.truncateNumberFormat(prc,4);
          credDeb = await this.truncateNumberFormat(credDeb,4);
        }
        else 
        {
          prc =  await this.truncateNumberFormat(prc,2);
          credDeb = await this.truncateNumberFormat(credDeb,2); 
        }
        extnd = await this.truncateNumberFormat(extnd,2);
        orgTot = await this.truncateNumberFormat(orgTot,2);
        currntTot = await this.truncateNumberFormat(currntTot,2);

        doc.Price = prc; 
        doc.Extended = extnd; 
        doc['Original total'] = orgTot;
        doc['Current Total'] = currntTot; 
        doc['Credit/Debit'] = credDeb; 
 
        rows.push(Object.values(doc));
        }

      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Purchase Order History'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }
 
  async generateSalesOrderExcelFile(userId: string) {
    
    let data = await this.connection.query("select upol.domestic_material_only,  upol.order_status_id as status, DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date' ,upoled.`transaction_type` as 'Event' ,'BRYZOS' as 'Purchaser', user.`email_id` as 'Delivered To' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Shipment Date' ,upo.`seller_po_number` as 'Bryzos PO' ,upo.`buyer_internal_po` as 'Your SO',upol.`po_line`as 'Line',upol.`description` as 'Description',upol.`actual_qty` as 'Qty',upol.`qty_unit` as 'Qty UM',upol.`seller_price_per_unit` as 'Price',upol.`price_unit` as 'Price UM' ,upol.`seller_line_total` as 'Extended',IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Bryzos Fees',upol.`actual_seller_line_total` as 'Original total',upol.`seller_price_per_unit` as 'Credit/Debit' , upol.`actual_seller_line_total` +  IF((upol.`seller_sales_tax`  IS NULL), 0.00 , upol.`seller_sales_tax`) + IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Current Total', upo.`seller_claimed_pdf_url` as 'URL To Download' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) Left join user on (upo.`buyer_id` = user.id)where upo.`seller_id` = "+userId+" and upo.`buyer_id` is not NULL ");

    if(data.length > 0){
      
      let rows = []
      const activeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERACTIVE);
      const canceledOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);
      const closeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCOMPLETED);

      for(let doc of data)
      {
        if(doc.domestic_material_only == 1) {
          doc.Description = doc.Description + '\n'+Constants.DOMESTIC_MATERIAL_VALUE;
        }
        if(doc.status == activeOrder.id){
          doc.status = Constants.ORDER_STATUS_OPEN;
        }else if(doc.status == canceledOrder.id){
          doc.status = Constants.ORDER_STATUS_CANCELED;
        }else if(doc.status == closeOrder.id){
          doc.status = Constants.ORDER_STATUS_CLOSE;
        }

        let prc = doc.Price; 
        let extnd = doc.Extended; 
         let orgTot = doc['Original total']; 
         let currntTot = doc['Current Total'];
         const unit = doc['Price UM'];
         let credDeb = doc['Credit/Debit'];
 
         if(unit == Constants.LB)
         {
           prc = await this.truncateNumberFormat(prc,4);
           credDeb= await this.truncateNumberFormat(credDeb,4);
         }
         else 
         {
           prc =  await this.truncateNumberFormat(prc,2);
           credDeb= await this.truncateNumberFormat(credDeb,2);
         }
 
         extnd = await this.truncateNumberFormat(extnd,2);
         orgTot = await this.truncateNumberFormat(orgTot,2);
         currntTot = await this.truncateNumberFormat(currntTot,2);
 
         doc.Price = prc; 
         doc.Extended = extnd; 
         doc['Original total'] = orgTot;
         doc['Current Total'] = currntTot; 
         doc['Credit/Debit'] = credDeb;

        rows.push(Object.values(doc));
      }
      
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Sales Order History'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)


      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async generatePayableStatementExcelFile(userId: string) {
    const data = await this.connection.query("select DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date of Purchase' , upo.`buyer_po_number` as 'Your PO', upo.`buyer_internal_po` as 'Bryzos SO',upo.`payment_method` as 'Method of Payment' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Delivery Date' , upol.`price_unit` as 'Price UM', upol.`buyer_price_per_unit` as 'Material Price',IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) as 'Sales Tax',upol.`actual_buyer_line_total` as 'Final total',IF((upoled.`buyer_paid`  IS NULL), 0.00 , upoled.`buyer_paid`) as `Total Paid` ,IF(( upoled.`buyer_purchase`  IS NULL), 0.00 ,  upoled.`buyer_purchase`) - IF((upoled.`buyer_paid`  IS NULL), 0.00 , upoled.`buyer_paid`) as 'Current Balance' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`buyer_id` = "+userId+" and upo.`is_active` = 1 and upol.`is_active` = 1 and upoled.`is_active` = 1 Group by upoled.purchase_order_line_id ORDER BY upoled.created_date DESC;");

    if(data.length > 0){
      
      let rows = []

      for(let doc of data)
      {   
        const unit = doc['Price UM'];

        let prc = doc['Material Price'];
        let finalTot = doc['Final total'];

        if(unit == Constants.LB)
        {
          prc = await this.truncateNumberFormat(prc,4);
        }
        else 
        {
          prc =  await this.truncateNumberFormat(prc,2);
        }

        finalTot = await this.truncateNumberFormat(finalTot,2); 

        doc['Material Price'] = prc;
        doc['Final total'] = finalTot;

        rows.push(Object.values(doc));
      }
  
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Accounts Payable Statement'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)
    

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async generateReceivableStatementExcelFile(userId: string) {
    const data = await this.connection.query("select DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date of Sale' , upo.`seller_po_number` as 'Bryzos PO', upo.`buyer_internal_po` as 'Your SO',upol.`actual_seller_line_total` + IF((upol.`seller_sales_tax`  IS NULL), 0.00 , upol.`seller_sales_tax`) as 'Final Order total' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`seller_id` = "+userId+" and upo.`buyer_id` is not NULL and upo.`is_active` = 1 and upol.`is_active` = 1 and upoled.`is_active` = 1;");

    if(data.length > 0){
      
      let rows = []
    
      for( let doc of data)
      {

        let ordTot =  doc['Final Order total'];
        ordTot = await this.truncateNumberFormat(ordTot,2); 
        doc['Final Order total'] = ordTot;

        rows.push(Object.values(doc));
      }
     
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Accounts Receivable Statement'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async getResaleCertByPONumber(po_number: string){
    const order = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':po_number,'is_active':true});
    if(order){
      const internal_po_number = order.buyer_internal_po;
      const buyer_id = order.buyer_id; 

      const resale_order = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository,buyer_id);

      let certificate_url = [];
      if(resale_order.length > 0){
        resale_order.forEach(value=>{
          if(value.is_active){
            certificate_url.push(value.cerificate_url_s3);
          }
        })
      }
      const finall_data = {
        "buyer_internal_po":internal_po_number,
        "resale_certificate_url":certificate_url
      }
      return finall_data;
    }else{
      return {'error_message':'Sorry there is no such PO exist'};
    }
  }

  async getUserProductTagMapping(userId) {
    let response = null;
    let productTags = await this.dbServiceObj.findMany(this.userProductTagMappingRepository,'user_id',userId);
    if(productTags.length > 0) {
      var finallData = {};
      for(let product of productTags) {
        let productId = product.product_id;
        let tag = product.tag;
        finallData[productId]=tag;
      }
      response = finallData;
    }
    return response;
  }

  async validateStateZip(payload){
    
    const state_id = payload.state_id;
    const zipcode = payload.zip_code;

    const state_data = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSalesTaxRepository,{state_id:state_id,zipcode:zipcode});

    if(state_data){
      return true;
    }else{
      return {"error_message":"The zip code and state code do not match."};

    }
  }
  
  async saveCreatePoOpenClose(payload,userId){
    let response = null;
    const date = new Date();
    const datetime = date.toISOString();

    const checkSession = await this.dbServiceObj.findOne(this.userCreatepoOpenClose,"session_id",payload.session_id);
    if(checkSession){
      if(payload.close_status){
        payload.close_time = datetime;
      }
      if(payload.shipping_details){
        let shippingDetails = payload.shipping_details;
        payload.line1 = shippingDetails.line1 ? shippingDetails.line1 : null;
        payload.city = shippingDetails.city ? shippingDetails.city : null;
        payload.zip = shippingDetails.zip ? shippingDetails.zip : null;
        payload.state_id = shippingDetails.state_id ? shippingDetails.state_id : null;
      }
      delete(payload.shipping_details);
      const update = await this.dbServiceObj.updateByMultipleWhere(payload,{"session_id":payload.session_id},this.userCreatepoOpenClose);

      if(payload.po_number){
        await this.dbServiceObj.updateByMultipleWhere({"po_number":payload.po_number},{"session_id":payload.session_id},this.userSearchAnalytics);
        const poLines = await this.dbServiceObj.findMany(this.userPurchaseOrderLineRepository,"buyer_po_number",payload.po_number);
        if(poLines){
          for(const poLine of poLines){
            const sellerPoNumber = payload.po_number.replace('S','P');
            const updateValues = {
              "buyer_po_number":payload.po_number,
              "quote_number":poLine.quote_number,
              "seller_po_number":sellerPoNumber,
              "buyer_calculation_price":poLine.buyer_calculation_price_per_unit,
              "seller_price_per_unit":poLine.seller_price_per_unit,
              "seller_extended":poLine.actual_seller_line_total,
              "seller_calculation_price":poLine.seller_calculation_price_per_unit,
            };
            await this.dbServiceObj.updateByMultipleWhere(updateValues,{"session_id":payload.session_id,"in_po_line":true,"po_line":poLine.po_line},this.userSearchLineDetailsRepository);
          }
        }
      }

      if(update){
        response = "Updated Successfully!";
      }
    }else{
      payload.open_time = datetime
      const save = await this.dbServiceObj.saveWithOutMapper(payload,userId,this.userCreatepoOpenClose);
      if(save){
        response = "Saved Successfully!";
      }
    }
    return response;
  }

  async saveCreatePoSearch(payload,userId){
    let response = null;
    const saveSearch = await this.dbServiceObj.saveWithOutMapper(payload,userId,this.userSearchAnalytics);
    if(saveSearch){
      response = "Saved Successfully!";
    }
    return response;
  }

  async saveOnBoardUserRequest(payload:OnBoardDto){
    let response = null;
    const onBoardPendinguserCheck = await this.dbServiceObj.findOne(this.userOnboradPendingRequestsRepository,'email_id',payload.email_id);
    const userCheck = await this.dbServiceObj.findOne(this.userRepository,'email_id',payload.email_id);
    if(onBoardPendinguserCheck || userCheck){
      return {"error_message":"This email-id already exist"};
    }

    const validateZip = await this.checkZipCodeExist(payload.zip_code);
    if(validateZip.hasOwnProperty("error_message")){
      return validateZip;
    }
    
    const company_name= payload.company_name.replace(/<[^>]*>/g, '').trim();
    const companyId = await this.signupUtility.checkUserMainCompanyExist(company_name);
    payload['company_name']=company_name;
    if(companyId){
      payload['company_id']=companyId;
    }else{
      const companyName = {
        'company_name': company_name
      }

      const insertNewCompany = await this.dbServiceObj.saveData(companyName,this.userPendingCompanyRequestsRepository);
    }

    const insert = await this.dbServiceObj.saveData(payload,this.userOnboradPendingRequestsRepository);
    if(insert){
      const referenceId = insert.id;
      const event = Constants.USER_ONBOARD_PENDING_REQUEST;
      let  messageBody= "Send user onboarding request";
      await this.awsQueue.sendDataToAwsQueue(referenceId,event,messageBody);
      response  = "Your request has been forwarded. Please wait for a response.";
    }else{
      response = {"error_message":"Something went wrong!, Please contact support"};
    }
    return response
  }

  async checkUserEmailAlreadyExist(payload:UserEmailDto) {
    const checkEmailExist = await this.signupUtility.checkUserEmailAlreadyExist(payload.email_id);
    if(checkEmailExist.hasOwnProperty("is_in_onBoard_request") || checkEmailExist.hasOwnProperty("is_user_onBoarded")){
      return { "error_message":"This email-id already exist" };
    }
    return "Email does not exist. You can proceed.";
  }

  async getCassAccessToken(userId) {
    let response = null;
    const axios = require('axios');
    //TODO constant in base library
    let cassPassword =  await this.baseLibraryService.getSecretValue(process.env.SM_ENV,Constants.VENDOR_CASS, Constants.PASSWORD);
    if(cassPassword) {
      const postBody = {
        userName: process.env.CASS_USER,
        password: cassPassword,
      };

      const headers = {
        'Content-Type': 'application/json',
      };

      try {
        const cassCurlResponse = await axios.post(process.env.CASS_URL, postBody, { headers });
        const jsonParsed = cassCurlResponse.data;
        response = jsonParsed?.token ?? null;
      } catch (error) {
        BryzosLogger.log(JSON.stringify(error.response.data), process.env.CASS_LOGGLY_TAG);
        return { "error_message": "Something went wrong" };
      }
    }
    return response;
  }

  async saveTooltip(payload:TooltipDto, userId)
  {   
    let tooltipResult = null;
    let updateTooltipDto = {};

    try{
      const userToolTip = await this.dbServiceObj.findOne(this.userRepository,'id', userId);
      
      // Ensure `tooltips` is parsed into an array
      let currentTooltips = [];
      if (userToolTip.tooltips) {
          try {
              currentTooltips = Array.isArray(userToolTip.tooltips) ? userToolTip.tooltips : JSON.parse(userToolTip.tooltips); // Parse JSON string into an array
          } catch (error) {
              console.error('Error parsing tooltips:', error);
              currentTooltips = []; // Fallback to an empty array in case of error
          }
      }

      // Step 2: Merge tooltips without duplicates
      const newTooltips = currentTooltips.concat(
          (Array.isArray(payload.tooltips) ? payload.tooltips : []).filter(tooltip => !currentTooltips.includes(tooltip))
      );

      // Step 3: Update the tooltips in the database
      updateTooltipDto = { tooltips: newTooltips}; // Convert back to JSON string

      tooltipResult = await this.dbServiceObj.updateByColumnId( this.userRepository, updateTooltipDto, 'id', userId);
        
      if(!tooltipResult) {
        return { "error_message": "Something went wrong! cannot save tooltip!" };
      }else {
        tooltipResult = "Tooltips saved.";
      }

    }catch(error) {
      return { "error_message": "Something went wrong! cannot save tooltip!" };
    }

    return updateTooltipDto; 
  }

  async getTooltip(userId)
  {
    let response = undefined;
    try {

      response = await this.dbServiceObj.findOne(
        this.userRepository,
        'id',
        userId,
      );

      if (!response) {
        return {
          "error_message": ' Sorry unable to fetch data from database. !',
        };
      }
     
    } catch (error) {
      console.log('Sorry unable to fetch data!!');
      response = {
        "error_message": ' Sorry unable to fetch data from database!',
      };
      return response;
    }
    return {tooltips:JSON.parse(response.tooltips)};
  }


  async deleteResaleCertificate(certId)
  {
    //fetch the certificate data from table 
    let certData = await this.dbServiceObj.findOne(this.userResaleCertificateRepository,'id',certId);

    if(!certData)
    return {"error_message":"Something went wrong!"}

    if(certData.status != Constants.Approved )
    {
      return {"error_message":"Certificate is not approved!"}
    }

    if(certData.is_deletable == false ){
        return {"error_message":"You cannot delete certificate uploaded by company"}
    }
   
    const updateDelCertDto = {
      is_active : 0
    }

    const isDeleted =  await this.dbServiceObj.updateByColumnId( this.userResaleCertificateRepository, updateDelCertDto, 'id', certId);

    if(!isDeleted)
      return {"error_message":"Unable to delete certificate!"};

      // send certificate id to aws sqs and trigger the email
      const buyerId = certData.user_id;

      await this.awsQueue.sendDeleteResaleCertAlert(certId);
      await this.awsQueue.sendDeleteResaleCertNotification(buyerId);

      return "Certificate deleted successfully!";
  }
  
  async savePoLineDetails(payload,userId){
    const currentDateTime = new Date();
    const foramtcurrentDateTime = await Utility.getCtDateTime(currentDateTime,'M/d/yy hh:mm a');

    let response = null;
    const uploadData = {};
    uploadData["session_id"] = payload.session_id ? payload.session_id : null;
    uploadData["po_line"] = payload.po_line ? payload.po_line : null;
    uploadData["line_session_id"] = payload.line_session_id ? payload.line_session_id : null;
    uploadData["in_po_line"] = payload.in_po_line ? payload.in_po_line : false;
    uploadData["description"] = payload.description ? payload.description : null;
    uploadData["qty"] = payload.qty ? payload.qty : null;
    uploadData["qty_unit"] = payload.qty_unit ? payload.qty_unit : null;
    uploadData["product_tag"] = payload.product_tag ? payload.product_tag : null;
    uploadData["price_unit"] = payload.price_unit ? payload.price_unit : null;
    uploadData["buyer_price_per_unit"] = payload.price ? payload.price : null;
    uploadData["buyer_extended"] = payload.extended ? payload.extended : null;
    uploadData["product_id"] = payload.product_id ? payload.product_id : null;
    uploadData["reference_product_id"] = payload.reference_product_id ? payload.reference_product_id : null;
    uploadData["shape"] = payload.shape ? payload.shape : null;
    uploadData["price_unit"] = payload.price_unit ? payload.price_unit : null;
    uploadData["domestic_material_only"] = payload.domestic_material_only ? payload.domestic_material_only : null;
    uploadData["user_id"] = userId;

    if(payload.product_id){
      const unitPrice = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository,"name",Constants.USER_SEARCH_LINE_DETAILS_QTY);
      const referenceProductData = await this.dbServiceObj.findOne(this.referenceDataProductsWidgetRepository,"Product_ID",payload.product_id);
      if(unitPrice && referenceProductData){
        const qtyUnit = unitPrice.value;
        uploadData["neutral_pricing"] = referenceProductData["Neutral_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["buyer_pricing"] = referenceProductData["Buyer_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["seller_pricing"] = referenceProductData["Seller_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["unit_price"] = qtyUnit;

        let buyer_delta_percent = ((Number(uploadData["buyer_pricing"]) - Number(uploadData["neutral_pricing"]) ) / Number(uploadData["neutral_pricing"]) ) * 100;
        let seller_delta_percent = ((Number(uploadData["seller_pricing"]) - Number(uploadData["neutral_pricing"]) ) / Number(uploadData["neutral_pricing"]) ) * 100;

        uploadData["buyer_delta_percent"] = parseFloat(buyer_delta_percent.toFixed(2));
        uploadData["seller_delta_percent"] = parseFloat(seller_delta_percent.toFixed(2));

      }
    }

    const checkLineSession = await this.dbServiceObj.findOneByMultipleWhere(this.userSearchLineDetailsRepository,{"session_id":payload.session_id,"line_session_id":payload.line_session_id});

    if(checkLineSession){
      uploadData["time_out"] = foramtcurrentDateTime;
      const update = await this.dbServiceObj.updateByMultipleWhere(uploadData,{"session_id":payload.session_id,"line_session_id":payload.line_session_id},this.userSearchLineDetailsRepository);
      if(!uploadData["in_po_line"]){
        await this.syncPoLines(payload.session_id,payload.po_line);
      }
      if(update){
        response = "Updated Successfully";
      }
    }else{
      uploadData["time_in"] = foramtcurrentDateTime;
      const save = await this.dbServiceObj.saveData(uploadData,this.userSearchLineDetailsRepository);
      if(save){
        response = "Saved Successfully";
      }
    }
    return response;
  }

  async checkZipCodeExist(zipCode){
    const checkZipCode = await this.signupUtility.checkZipCodeValidation(zipCode)
    if(checkZipCode.hasOwnProperty('error_message')){ return checkZipCode }
    return true;
  }

  async syncPoLines(sessionId,poLineNo){
    const poLines = await this.dbServiceObj.findMany(this.userSearchLineDetailsRepository,"session_id",sessionId);
    for(const poLine of poLines){
      if(Number(poLine.po_line) > Number(poLineNo)){
        let newPoLineNo = Number(poLine.po_line) - 1;
        await this.dbServiceObj.updateByMultipleWhere({"po_line":newPoLineNo},{"session_id":sessionId,"line_session_id":poLine.line_session_id},this.userSearchLineDetailsRepository);
      }
    }
  }

 /* async truncateDecimal(number: number, decimalPlaces: number): number {
    return Number(number.toFixed(decimalPlaces));
  }*/

   async truncateNumberFormat(amt,afterDecimalCount = 2){
    const decimalPosition: number = amt.indexOf('.') + 1;
    const digitLimit: number = decimalPosition + afterDecimalCount;
    const finalNumber: string = amt.substring(0, digitLimit);
    let numberFormat: string = Number(finalNumber).toLocaleString('en-US', {
      minimumFractionDigits: afterDecimalCount,
      maximumFractionDigits: afterDecimalCount
    });
    return numberFormat;
  }
  
  async getMainCompanyList() {

    let response = [];

    let userCompanyClientLocations = await this.dbServiceObj.selectCustomFields(this.userRepository,"is_active","1",["distinct(client_company) as clientCompany","company_id as companyId"]);
    const clientCompanyId = {};
    for(const userCompanyClientLocation of userCompanyClientLocations){
      if(userCompanyClientLocation.clientCompany && userCompanyClientLocation.companyId){
        if(clientCompanyId.hasOwnProperty(userCompanyClientLocation.companyId)){
          clientCompanyId[userCompanyClientLocation.companyId].push(userCompanyClientLocation.clientCompany);
        }else{
          clientCompanyId[userCompanyClientLocation.companyId]=[];
          clientCompanyId[userCompanyClientLocation.companyId].push(userCompanyClientLocation.clientCompany);
        }
      }
    }
    const companies = await this.dbServiceObj.selectCustomFieldsOrderByCreatedDate(this.userMainCompanyRepository, "is_active", "1", "*", "DESC");
    if(companies.length > 0) {
      for(let company of companies){
        let clientCompany=[]; 
        if(clientCompanyId.hasOwnProperty(company.id)){
            clientCompany=clientCompanyId[company.id];
        }
        const data = {
          'id': company.id,
          'company_name':company.company_name,
          'client_company':clientCompany,
          'disc_discount_period':company.deprecated_disc_discount_period,
          'disc_discount_phaseout_startdate':company.deprecated_disc_discount_phaseout_startdate,
          'disc_discount_phaseout_period':company.deprecated_disc_discount_phaseout_period,
          'disc_discount_percentage':company.buyer_spread_percentage,
          'disc_discount_pricing_column':company.base_pricing_column,
          'seller_spread_rate':company.seller_spread_rate,
        };
        
        response.push(data);
      }
      return response;
    }else{
        return {'error_message':'No company found!'};
    }    
  }

  async saveSearchPriceData(payloads,userId){
    let response = null;

    try{
      if(payloads && userId){
        for(let payload of payloads){
          payload["user_id"] = userId;
          let priceJson = {};
          if(payload.hasOwnProperty("product_id")){
            const productData = await this.dbServiceObj.findOne(this.referenceDataProductsWidgetRepository,"Product_ID",payload["product_id"])
            if(productData){
                payload['Neutral_Pricing_Ft']=Number(productData.Neutral_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_Ea']=Number(productData.Neutral_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_LB']=Number(productData.Neutral_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_CWT']=Number(productData.Neutral_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_Net_Ton']=Number(productData.Neutral_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Ft']=Number(productData.Buyer_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Ea']=Number(productData.Buyer_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_LB']=Number(productData.Buyer_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_CWT']=Number(productData.Buyer_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Net_Ton']=Number(productData.Buyer_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Ft']=Number(productData.Seller_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Ea']=Number(productData.Seller_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_LB']=Number(productData.Seller_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_CWT']=Number(productData.Seller_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Net_Ton']=Number(productData.Seller_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['domestic_material_only']=productData.domestic_material_only == 1 ? true : false;
            }
          }

          const whereConditions = {
            "session_id":payload["session_id"],
            "line_session_id":payload["line_session_id"],
            "user_id":userId
          }

          let checkUserSession = await this.dbServiceObj.findOneByMultipleWhere(this.userSearchPriceDetailsRepository,whereConditions);
          if(checkUserSession){
            await this.dbServiceObj.updateByMultipleWhere(payload,whereConditions,this.userSearchPriceDetailsRepository);
          }else{
            const currentDateTime = new Date();
            const foramtcurrentDateTime = await Utility.getCtDateTime(currentDateTime,'M/d/yy hh:mm a');
            payload["search_time"]=foramtcurrentDateTime;
            await this.dbServiceObj.saveData(payload,this.userSearchPriceDetailsRepository);
          }
        }
      }
      response = "Saved Successfully!";
    }catch(er){
      response = "Something went wrong!";
    }
    return response;
  }

  async saveSearchPriceMoveOutScreen(payload,userId){
    payload["user_id"]=userId;
    await this.dbServiceObj.saveData(payload,this.userSearchPriceScreenMoveOutRepository);
    return "Saved SuccessFully!";
  }

  async updateUser(updatePayload, userId)
  {
    await this.dbServiceObj.updateWithoutMapper(updatePayload, 'id', userId, this.userRepository);
  }

  async deleteUser(userId)
  {
    let userData = await this.dbServiceObj.findOne(this.userRepository,'id',userId);

    if(!userData) {
      await this.dbServiceObj.saveData({user_id: userId, status: 'DELETE_EXCEPTION'}, this.userDeleteAccountRepository);
      return { "error_message" : "User not found!" }
    } else if(userData && userData.is_super_admin) {
      return { "error_message" : "This user cannot be deleted." }
    }
  
    const updateDelUserDto = { is_active : 0 }

    const isDeleted = await this.dbServiceObj.updateByColumnId( this.userRepository, {"is_active" : 0, "status": Constants.USER_DELETED}, 'id', userId);

    if(!isDeleted){
      await this.dbServiceObj.saveData({user_id: userId, status: 'DELETE_EXCEPTION'}, this.userDeleteAccountRepository);
      return {"error_message":"Unable to delete user!"};
    }


    if(userData.type === Constants.BUYER){
      await this.dbServiceObj.updateByColumnId( this.userBuyingPreferenceRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.companyBuyNowPayLaterRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.userResaleCertificateRepository, updateDelUserDto, 'user_id', userId);
    }
    
    if(userData.type === Constants.SELLER){
      await this.dbServiceObj.updateByColumnId( this.userSellingPreferenceRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.userArPaymentInfoRepository, updateDelUserDto, 'user_id', userId);
    }
    await this.dbServiceObj.updateByColumnId( this.paymentInfoRepository, updateDelUserDto, 'user_id', userId);
      
    await this.dbServiceObj.saveData({user_id: userId, status: 'DELETED'}, this.userDeleteAccountRepository);


    await this.awsQueue.globalSignOutUser(userData);

    
    return "User deleted successfully!";

  }

  async createPdf(pdfData) {
    if (!pdfData?.data || !pdfData?.file_name) {
      return { "error_message": "invalid request object" };
    }
    try {
      let response = await this.pdfMakerService.makePdf(pdfData, pdfData.file_name);
      return response;
    } catch (err) {
      return { "error_message": err?.message || "sorry, unable to create pdf." };
    }
  }

  async userSpreadData(userId:string){
    let response = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(user){
      response = {
        is_discount: user.is_buyer_spread,
        discount_rate: user.buyer_spread_rate,
        discount_pricing_column: user.base_pricing_column,
        discount_period: user.deprecated_disc_discount_period,
        discount_phaseout_startdate: user.deprecated_disc_discount_phaseout_startdate,
        discount_phaseout_period: user.deprecated_disc_discount_phaseout_period,
        is_discount_var_overriden: user.is_buyer_spread_overriden,
      }
    }
    return response;
  }

  async getBryzosStatesCertUrl() {
    const conditions = [Constants.RESALE_CERTIFICATE_BUCKET_NAME, Constants.RESALE_CERTIFICATE_FOLDER, Constants.RESALE_CERTIFICATE_FILES];
    const getReferenceData = await this.dbServiceObj.findManyByWhereIn(this.referenceDataSettingsRepository, "name", conditions)
    let referenceDataValueObj: any = {}
    getReferenceData.forEach((data: any) => {
      referenceDataValueObj[data.name] = data.value
    })
    const bucketName = referenceDataValueObj[Constants.RESALE_CERTIFICATE_BUCKET_NAME];
    const folderName = referenceDataValueObj[Constants.RESALE_CERTIFICATE_FOLDER];
    const fileName = referenceDataValueObj[Constants.RESALE_CERTIFICATE_FILES];

    const bryzosStateCertUrl = `https://${bucketName}.s3.amazonaws.com/${folderName}/${fileName}.pdf`;
    return bryzosStateCertUrl;
  }

  /* async createRoom(payload: ChatRoomDto) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v1/chatroom`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e.response.data.error.details);
    }
  } */

  async createUser(userId: number) {
    const userData = await this.dbServiceObj.findOne(this.userRepository, "id", userId.toString());
    if (!userData) {
      return { "error_message": "user not found" };
    }

    const userIdentifier = `${process.env.DEAD_SIMPLE_CHAT_UNIQUE_ID_PREFIX}${userId.toString()}`;
    try {
      // check user already exist or not

      const response = (await axios.get(`${this.baseUrl}/consumer/api/v2/user/${userIdentifier}`, { params: this.params })).data;
      return response.uniqueUserIdentifier;

    } catch (error) {
      // user not exist, so create new user

      const userName = `${userData.first_name}_${userData.last_name}`;
      const payload = {
        uniqueUserIdentifier: userIdentifier,
        username: userName,
        firstName: userData.first_name,
        lastName: userData.last_name,
        email: userData.email_id,
        membershipDetails: { roleName: "user", roomId: this.roomId }
      };

      try {
        const response = (await axios.post(`${this.baseUrl}/consumer/api/v2/user`, payload, { params: this.params })).data;
        if (!response) {
          return { "error_message": "Sorry unable to create user" }
        }

        const uniqueUserIdentifier = response.user.uniqueUserIdentifier;
        const saveDto = {
          access_token: response.accessToken,
          id: response.user._id,
          unique_user_identifier: uniqueUserIdentifier,
          user_name: response.user.username,
          email: response.user.email,
          is_moderator: response.membershipDetails.roleName === "moderator",
          room_id: response.membershipDetails.roomId,
          external_user_id: userId.toString(),
        };

        await this.dbServiceObj.saveData(saveDto, this.chatUsersRepository);
        await this.dbServiceObj.updateByColumnId(this.userRepository, { chat_unique_user_identifier: uniqueUserIdentifier }, "id", userId);

        return uniqueUserIdentifier;

      } catch (error) {
        const errorObj = error?.response?.data?.error;

        BryzosLogger.log(JSON.stringify({ error: errorObj, event: "create user in DeadSimple chat error" }), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
        return { "error_message": "Sorry unable to create user in DeadSimple chat" };
      }
    }
  }

  /* async getListOfChannels(roomId: string) {
    try {
      const response = (await axios.get(`${this.baseUrl}/consumer/api/v1/chatroom/${roomId}/channels`, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async createChannelAddUsers(data: ChannelAddUsersDto) {
    try {
      let response = null;

      const buyer = await this.dbServiceObj.findOne(this.userRepository, "id", data.buyer_id);
      if (!buyer) {
        return { "error_message": "user not found" };
      }
      const buyerDto = {
        chatRoom: this.roomId,
        externalUserId: buyer.id?.toString(),
        isModerator: false,
        email: buyer.email_id,
        username: `buyer_${buyer.first_name}_${buyer.last_name}`,
      };
      const buyerData = await this.createUserIfNotExist(buyerDto, buyer.chat_unique_user_identifier);

      const seller = await this.dbServiceObj.findOne(this.userRepository, "id", data.seller_id);
      if (!seller) {
        return { "error_message": "user not found" };
      }
      const sellerDto = {
        chatRoom: this.roomId,
        externalUserId: seller.id?.toString(),
        isModerator: false,
        email: seller.email_id,
        username: `seller_${seller.first_name}_${seller.last_name}`,
      };
      const sellerData = await this.createUserIfNotExist(sellerDto, seller.chat_unique_user_identifier);

      const moderator = await this.dbServiceObj.findOne(this.userRepository, "id", data.moderator_id);
      if (!moderator) {
        return { "error_message": "user not found" };
      }
      const moderatorDto = {
        chatRoom: this.roomId,
        externalUserId:moderator.id?.toString(), 
        isModerator: true,
        email: moderator.email_id,
        username: `moderator_${moderator.first_name}_${moderator.last_name}`,
      };
      const moderatorData = await this.createUserIfNotExist(moderatorDto, moderator.chat_unique_user_identifier);

      const existingChannelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", data.channel_name);
      if (!existingChannelData) {
        const payload = { enabled: true, notifyAllUsers: false, channelName: data.channel_name };
        response = (await axios.post(`${this.baseUrl}/consumer/api/v1/chatroom/${this.roomId}/channel`, payload, { params: this.params }))?.data;

        const memberIds = JSON.stringify([buyerData.uniqueUserIdentifier, sellerData.uniqueUserIdentifier, moderatorData.uniqueUserIdentifier]);

        const saveDto = { id: response._id, channel_name: response.channelName, enabled: response.enabled, notify_all_users: response.notifyAllUsers, room_id: response.roomId, members_unique_user_identifier: memberIds };
        const result = await this.dbServiceObj.saveData(saveDto, this.chatChannelsRepository);
        
        return {
          _id: response._id,
          channelName: data.channel_name,
          [data.buyer_id]: buyerData,
          [data.seller_id]: sellerData,
          [data.moderator_id]: moderatorData,
        }
      } else {
        return {  _id: existingChannelData.id,
          channelName: data.channel_name,
          [data.buyer_id]: buyerData,
          [data.seller_id]: sellerData,
          [data.moderator_id]: moderatorData, };
      }

    } catch (e) {
      console.log(e);
    }
  }

  async createUserIfNotExist(payload: any, uniqueUserIdentifier: string) {
    try {
      let existingUser = null;
      try {
        existingUser = (await axios.get(`${this.baseUrl}/consumer/api/v2/user/${uniqueUserIdentifier}`, { params: this.params }))?.data;
      } catch (e) {
        // user not found , create new user
        console.log(e?.response?.data?.message);
      }
      if (!existingUser) {
        const response = (await axios.post(this.baseUrl + '/consumer/api/v1/user', payload, { params: this.params })).data;
        const saveDto = {
          id: response.userId,
          access_token: response.accessToken,
          unique_user_identifier: response.uniqueUserIdentifier,
          user_name: response.username,
          email: payload.email,
          is_moderator: response.isModerator,
          room_id: payload.chatRoom,
          external_user_id: payload.externalUserId,
        };
        const saveResult = await this.dbServiceObj.saveData(saveDto, this.chatUsersRepository);
        const updateDto = await this.dbServiceObj.updateByColumnId(this.userRepository, {chat_unique_user_identifier: response.uniqueUserIdentifier}, "id", payload.externalUserId);
        return response;
      } else {
        if (existingUser.isModerator) {
          const user = await this.dbServiceObj.findOne(this.chatUsersRepository, "id", existingUser._id);
          if (user) {
            existingUser.accessToken = user.access_token;
          }
        }
        return existingUser;
      }
    } catch (e) {
      console.log(e);
    }
  }

  async getAllAcceptedBuyerOrders(userId: number) {
    const conditions: any = [
      { "column": "claimed_by", "operator": "NOT IN", "value": ["PENDING", "READY_TO_CLAME"] },
      { "column": "is_active", "operator": "=", "value": true },
    ];
    if (userId !== 883) {
      conditions.push({ "column": "buyer_id", "operator": "=", "value": userId });
    }

    return await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, null, conditions);
  }

  async getAllAcceptedSellerOrders(userId: number) {
    const conditions: any = [
      { "column": "claimed_by", "operator": "NOT IN", "value": ["PENDING", "READY_TO_CLAME"] },
      { "column": "is_active", "operator": "=", "value": true },
    ];
    if (userId !== 883) {
      conditions.push({ "column": "seller_id", "operator": "=", "value": userId },);
    }

    return await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, null, conditions);
  }

  async getAllUsersInChannel(channelName: string) {
    try {
      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      const membersUniqueUserIdentifier = JSON.parse(channelData.members_unique_user_identifier);
      const blockedMembersUniqueUserIdentifier = JSON.parse(channelData.blocked_members_unique_user_identifier);
      const ids = membersUniqueUserIdentifier.map(id => id);
      const chatUsersData = await this.dbServiceObj.findManyByWhereIn(this.chatUsersRepository, "unique_user_identifier", ids);
      chatUsersData.forEach(data => {
        const uniqueUserIdentifier = data.unique_user_identifier;
        const isBlocked = !!blockedMembersUniqueUserIdentifier.find(id => id === uniqueUserIdentifier);
        data.is_user_blocked = isBlocked;
      });

      return chatUsersData;
    } catch (e) {
      console.log(e);
    }
  }

  async blockUserInChannel(channelName: string, uniqueUserIdentifier: string) {
    try {
      let blockedMemberIds = [];
      let blockedMemberIdsText = "";

      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      blockedMemberIds = JSON.parse(channelData.blocked_members_unique_user_identifier);
      if (!blockedMemberIds.find(id => id === uniqueUserIdentifier)) {
        blockedMemberIdsText = JSON.stringify([...blockedMemberIds, uniqueUserIdentifier]);
        await this.dbServiceObj.updateByColumnId(this.chatChannelsRepository, { blocked_members_unique_user_identifier: blockedMemberIdsText }, "channel_name", channelName);
      } else {
        return { "error_message": "user already blocked" }
      }

      return "Sucess";
    } catch (e) {
      console.log(e);
    }
  }

  async unBlockUserInChannel(channelName: string, uniqueUserIdentifier: string) {
    try {
      let blockedMemberIds = [];
      let blockedMemberIdsText = "";

      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      blockedMemberIds = JSON.parse(channelData.blocked_members_unique_user_identifier);
      blockedMemberIds = blockedMemberIds.filter(id => id !== uniqueUserIdentifier);
      blockedMemberIdsText = JSON.stringify(blockedMemberIds);
      await this.dbServiceObj.updateByColumnId(this.chatChannelsRepository, { blocked_members_unique_user_identifier: blockedMemberIdsText }, "channel_name", channelName);
      return "Sucess";
    } catch (e) {
      console.log(e);
    }
  }

  async makeUpdateChatRoomMember(roomId: any, payload: any) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v2/room/${roomId}/member`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async createModerator(payload: any) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v1/user`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async deleteChatRoom() {
    try {
      const response = (await axios.delete(`${this.baseUrl}/consumer/api/v1/chatroom/${this.roomId}`, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  } */
  
  async saveForegroundBackgroundActivity(userId: string, foregroundBackgroundActivityDto: ForegroundBackgroundActivityDto, superAdminUserId: string) {
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if (!user) {
      return "Sorry user not found!";
    }
    let logoutResponse = await this.dbServiceObj.saveWithOutMapper(
      {
        email_id: user.email_id,
        event: foregroundBackgroundActivityDto.event,
        os_version: foregroundBackgroundActivityDto.os_version,
        ui_version: foregroundBackgroundActivityDto.ui_version,
        last_login_app_version: foregroundBackgroundActivityDto.last_login_app_version,
        device_id: foregroundBackgroundActivityDto.device_id,
        super_admin_user_id: superAdminUserId,
      },
      user.id,
      this.userLogger,
    );
    if (logoutResponse) {
      return "Activity  successfully saved!";
    }
  }

  async checkUserApproval(emailId: string) {
    const user = await this.dbServiceObj.findOne(this.userRepository, 'email_id', emailId);
    if (!user) {
      return false;
    }
    return user.is_cognito_confirmed === 1 ? true : { error_message: "Please contact support!" };
  }

  async userSignup(payload: CreateUserDto) {
    let response = null;
    const emailId = payload.email_id;
    let isCompanyValid = true;
    
    const userCheck = await this.dbServiceObj.findOne(this.userRepository, 'email_id', emailId);
    
    if(userCheck){ return {"error_message":"This email id already exist"}; }
 
    const isZipCodeValidate = await this.signupUtility.checkZipCodeValidation(payload.zip_code)

    if(isZipCodeValidate.hasOwnProperty("error_message")){ return isZipCodeValidate }
    
    payload['state_id'] = isZipCodeValidate.state_id;

    const isEmailValid = await this.signupUtility.checkIfEmailExists(emailId, 'WIDGET_SIGNUP_USER');
    //Check if email is valid & present in sign_up_pre_approved_email table. If yes, we need to signup user and login. If no, we need to send user for Kelsey's approval
    let checkInPreApprovedEmail = await this.dbServiceObj.findOne(this.signUpPreApprovedEmailRepository,'email_id',emailId);
    
    //Company condition check
    const validateCompany = await this.signupUtility.validateAndSaveCompany(payload.company_name);

    payload['company_id'] = validateCompany['company_id']

    if(validateCompany.hasOwnProperty('is_company_valid') && !validateCompany['is_company_valid']){ isCompanyValid = false }

    payload['company_name'] = payload.company_name.replace(/<[^>]*>/g, '').trim();

    if(checkInPreApprovedEmail && isEmailValid && isCompanyValid) {
      payload['onboarded_app'] = checkInPreApprovedEmail?.cohort;
      const widgetUser = await this.createWidgetUserData(payload);
      if (widgetUser?.error_message) {
        return widgetUser;
      } else if(widgetUser) {
        response = { is_approved: true }; //UI uses this to show appropriate message if user can login or has to wait for approval.
      }
    } else {
      const onboardRequest = await this.dbServiceObj.saveData(payload, this.userOnboradPendingRequestsRepository);
      if(onboardRequest){
        const referenceId = onboardRequest.id;
        const event = Constants.USER_ONBOARD_PENDING_REQUEST;
        let  messageBody = "Send user onboarding request";
        await this.awsQueue.sendDataToAwsQueue(referenceId, event, messageBody);
        response  = { is_approved: false }; //UI uses this to show appropriate message if user can login or has to wait for approval 
      }
    }    
    return response;
  }

  async createWidgetUserData(userData) {
    try{
      let bryzosTermsConditionId = null;
      let acceptedTermsAndCondition = null;
  
      if(!userData){
        return {"error_message":"Something went wrong!"};
      }   

      //If company is not present in DB, we will insert new company as approved company i.e. new company's approval not required for not rejected companies
      if(!userData?.company_id){
        const saveCompany = (await this.dbServiceObj.saveData({'company_name': userData.company_name}, this.userMainCompanyRepository)).id;
        userData['company_id'] = saveCompany;

        //Deactivate if there is existing entry in pending request to avoid duplicate entries
        this.dbServiceObj.updateByMultipleWhere({ "is_active" : false },{ "company_name": userData.company_name, "is_approved": 'IS NULL', "is_active": true },this.userPendingCompanyRequestsRepository);
      }

      const mappedDeviceIds = [];
      if (userData?.device_id) {
        mappedDeviceIds.push(userData.device_id);
      }

      const insertUserData: any = {
        email_id: userData.email_id,
        first_name: userData.first_name,
        last_name: userData.last_name,
        is_email_verified: process.env.AWS_COGNITO_EMAIL_VERIFIED === "TRUE",
        type: userData.user_type,
        cognito_user_name: userData.cognito_user_name.trim(),
        client_company: userData.client_company,
        pusher_id: this.uniqueId.rnd(13),
        accepted_terms_and_condition: userData.accepted_terms_and_condition,
        company_id: userData?.company_id,
        company_name: userData.company_name,
        os_version: userData?.os_version,
        onboarded_app: userData?.onboarded_app,
        last_login_app_version: userData?.last_login_app_version,
        mapped_device_ids: mappedDeviceIds.length > 0 ? JSON.stringify(mappedDeviceIds) : null,
      }
  
  
      if (userData.user_type === Constants.BUYER) {
        await this.signupUtility.getSpreadSettings(insertUserData);
      }
  
      delete (insertUserData.company_name);
      delete (insertUserData.source);
  
      const saveUser = (await this.dbServiceObj.saveData(insertUserData, this.userRepository)).id;
      if (saveUser) {
        const userLog = {
          user_id : saveUser,
          event: Constants.ONBOARD_TYPE_SIGNUP_USER,
          email_id: insertUserData.email_id,
          zip_code: userData.zip_code,
          os_version: userData?.os_version,
          ui_version: userData?.ui_version,
          last_login_app_version: userData?.last_login_app_version, 
          device_id: userData?.device_id
        }

        await this.dbServiceObj.updateByMultipleWhere({ 'is_signed_up': true }, { 'email_id': userData.email_id }, this.signUpPreApprovedEmailRepository);

        await this.dbServiceObj.saveData(userLog,this.userLogger);

        await this.signupUtility.saveUniqueKeyForUser(saveUser);
  
        const saveUserPreference = await this.signupUtility.saveUserPreference(userData, saveUser);
        
        if(saveUserPreference?.error_message){ return saveUserPreference; }

        acceptedTermsAndCondition = userData.accepted_terms_and_condition;
        bryzosTermsConditionId = userData.bryzos_terms_condtion_id;

        if (acceptedTermsAndCondition && bryzosTermsConditionId) {
          const insertTncData = {
            user_id: saveUser,
            bryzos_terms_condtion_id: bryzosTermsConditionId,
            terms_conditions_version: acceptedTermsAndCondition,
          }
          await this.dbServiceObj.saveData(insertTncData, this.widgetTermsConditionRepository);
        }
      }else{
        return { error_message: 'Something went wrong' };
      }
      await this.awsQueue.sendNewUserSignUpEmail(saveUser.toString());
      return saveUser;
    } catch (error) {
      BryzosLogger.log(JSON.stringify(error.response.data), 'SIGNUP_USER_LOGGLY_LOG_ERROR_TAG');
      return { error_message: 'Something went wrong' };
    }
  }

  async confirmUser(username: string) {
    let response = null;
    try {
      response = await this.cognitoAuthService.confirmUserSignUp(username);
    } catch(confirmusererror) {
      response = {'error_message': 'Error confirming user. Please contact admin'};
    }
    return response;
  }
  
  async migratedToPasswordless(payload:MigrateToPasswordLessDto){
    let emailId = payload.email_id;
    try {
      // password never changed = 0 (Only for old/existing pending users)
      // reset password = 1 (via A.D. Reset)
      // passwordless migrated done (user self set) = 2 (Password has been set by user).
      await this.dbServiceObj.updateByMultipleWhere({is_migrated_to_password : 2},{email_id : emailId},this.userRepository);
      return "Migrated to password successfully";
    } catch(error) {
      return { error_message : "Something went wrong!!" };
    }
  }

  //This API is called after change password and forgot password to make sure user has been logged out of all devices.
  async globalSignout(payload:GlobalSignoutDto) {
    let emailId = payload.email_id;
    let deviceId = payload.device_id;
    try{
      let user = await this.dbServiceObj.findOne(this.userRepository, 'email_id', emailId);
      let globalSignoutResponse = await this.awsUtility.userGlobalSignOut(user.cognito_user_name);

      // send websocket event to UI for this user's all devices
      if(globalSignoutResponse) {
        Utility.sendDataToWebsocket({email_id : emailId, device_id : deviceId}, 'passwordChanged');
        return true;
      }
    } catch(error) {
      return { error_message : "Something went wrong!!" };
    }
  }

  async getVideos(tagsArr: string[]) {
    let videoList = [];
    let response = {};
    if (tagsArr.length > 0) {
      let columnContditions = [
          { columnName: 'tags', operator: 'IN', value: tagsArr },
          { columnName: 'is_active', operator: '=', value: true },
          { columnName: 'show_on_ui', operator: '=', value: true },
      ]
      let orderBy = {
        sequence  : "ASC"
      }
      videoList = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetVideoLibraryRepository,columnContditions,"*",orderBy);
      
      for (const tag of tagsArr) {
        const filteredRows = videoList.filter(row => row.tags === tag);
        if (filteredRows.length > 0) {
          response[tag] = filteredRows;
        }
      }
    }
    return Object.keys(response).length > 0 ?  response  :  null ;
  }

  async saveVideoViewCount(payload:VideoViewDto){
    try{
      await this.dbServiceObj.saveData(payload,this.videoLibraryUserViewLogRepository);
      let updateCondition = { video_id : payload.video_id};
      let updateDto = {
        video_id : payload.video_id,
        view_count: () => `view_count + 1` 
      }
      await this.dbServiceObj.updateByMultipleWhere(updateDto,updateCondition,this.extendedWidgetVideoLibraryRepository);
      let video = await this.dbServiceObj.findOne(this.extendedWidgetVideoLibraryRepository, 'video_id', payload.video_id);
      Utility.sendDataToWebsocket({tag:video.tags, view_count:video.view_count, video_id: video.video_id}, 'notifyVideoViewCount');
      return "view count saved successfully";
    }catch(error){
      console.log("save video view error : ",error);
      return {
        error_message : "Something went wrong!!"
      }
    }
  }

  async saveShareVideo(){
      return "Video shared successfully";
  }

  async saveShareVideoAnalytics(payload:ShareVideoAnalyticsDto,userId:string){
    try{
      let payloadData = payload;
      const columnContditions = [
        { columnName: 'video_id', operator: '=', value: payload.video_id },
        { columnName: 'is_active', operator: '=', value: true },
        { columnName: 'share_video_url', operator: 'IS NOT NULL' },
      ]
      const checkSharedUrl = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetVideoLibraryRepository,columnContditions);

      if(checkSharedUrl && checkSharedUrl.length === 0){ return { error_message : "This video cannot be shared" }  }
      
      const sharedUrl = checkSharedUrl[0].share_video_url;
      const title = checkSharedUrl[0].title;
      const queryParam = checkSharedUrl[0].tags;

      if(sharedUrl.trim() === ""){ return { error_message : "This video cannot be shared"} }

      payloadData['user_id'] = userId;
      payloadData['video_url'] = sharedUrl;
      payloadData['title'] = title;
      payloadData['query_param'] = queryParam;
      const insert = await this.dbServiceObj.saveData(payloadData,this.shareVideoAnalyticsRepository);
      if(payloadData.share_via === LibConstants.VIDEO_SHARE_VIA_EMAIL){
        await this.awsQueue.sendShareVideoToEmailId(insert.id);
      }
      return "share video logged successfully";
    }catch(error){
      BryzosLogger.log(JSON.stringify({ error: error, event: "SAVE_SHARE_VIDEO_ANALYTICS" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message : "Something went wrong!!" }
    }
  }

  async shuffleSequence(){
    try{
      const queueURL = process.env.SELLER_INVOICE_READ_QUEUE;
      const messageAttributes= {
        "event": {
          DataType: "String",
          StringValue: "RANDOM_SEQUENCE_SHUFFLE_OF_VIDEOS"
        }
      };
      const messageBody= "random sequence shuffling of videos";
      await this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
      return "videos are send for shuffling";
    }catch(err){
      return { error_message : "Something went wrong!!" };
    }
  }
  
  async getAccessToken(){
    let response = null;
    const date = new Date();
    const expireMinutes = parseInt(process.env.TRUEVAULT_EXPIRE_ACCESS_TOKEN_MIN);
    date.setMinutes(date.getMinutes() + expireMinutes);
    
    const notValidAfter = date.toISOString().replace(/\.\d{3}Z$/, '.000Z');
    const url = process.env.TRUEVAULT_AUTH_LOGIN_URL;

    const params = {
        username: process.env.TRUEVAULT_USERNAME ,
        password: process.env.TRUEVAULT_PASSWORD ,
        account_id: process.env.TRUEVAULT_ACCOUNT_ID ,
        not_valid_after: notValidAfter,
    };

    try{
      const responseData = await axios.post(url, new URLSearchParams(params).toString(), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      response = responseData.data;
    }
    catch(error){
      return { "error_message" : "Something went wrong" }
    }
    const accessToken = response.user.access_token ?? null;
    return accessToken
  }

  async getBryzosW9FormUrl() {
    let w9Paths = await this.dbServiceObj.findManyByWhereIn(this.referenceDataSettingsRepository,"name",[Constants.RESALE_CERTIFICATE_BUCKET_NAME,Constants.W9_FILE_NAME]);

    const bucketName = w9Paths.find(item => item.name === Constants.RESALE_CERTIFICATE_BUCKET_NAME)?.value;
    const fileName = w9Paths.find(item => item.name === Constants.W9_FILE_NAME)?.value;
    
    return (bucketName && fileName) 
      ? `https://${bucketName}.s3.amazonaws.com/${fileName}.pdf` 
      : (console.log('Required data not found. Please check the input array.'), null);
  }

  async getSavedBom(userId:string, bomId:string){
    try{
      const leftJoins = [
        {"table":"user_bom_product_extract","joinColumn":"bom_upload_id","mainTableColumn":"id"}
      ];

      let conditions = []

      if(bomId){
        conditions.push({column:"id",operator:"=", value:bomId});
      }
      conditions.push({column:"user_id",operator:"=", value:userId});
      conditions.push({"column":"is_active","operator":"=","value":true});
      conditions.push({"column":"is_active","operator":"=","value":true, "table":"user_bom_product_extract"});

      const orderBy = {
        'table1.created_date': 'DESC',
        'user_bom_product_extract.created_date': 'ASC',
        'user_bom_product_extract.current_page': 'ASC',
        'user_bom_product_extract.product_index': 'ASC'
      }

      const mapperFields = {
        'selectFields': [
          'table1.id AS bom_upload_id',
          'table1.original_file_name AS file_name',
          'table1.original_file_name AS actual_file_name',
          'table1.attempt AS attempt',
          'table1.unique_identifier AS unique_identifier',
          'table1.status AS bom_status',
          'table1.s3_url AS s3_url',
          'table1.device_id AS device_id',
          'table1.buyer_po_price AS material_total',
          'table1.sales_tax AS sales_tax',
          'table1.total_weight AS total_weight',
          'table1.review_status AS review_status',
          'table1.line1 AS line1',
          'table1.line2 AS line2',
          'table1.city AS city',
          'table1.state_id AS state_id',
          'table1.zip AS zip',
          'table1.bom_name AS title', 
          'table1.bom_type AS type',
          'DATE_FORMAT(CONVERT_TZ(table1.delivery_date,"UTC","America/Chicago"), "%m/%d/%Y %h:%i %p") AS delivery_date',
          'table1.created_date AS created_date',
          'user_bom_product_extract.id AS id',
          'user_bom_product_extract.original_line_status AS original_line_status',
          'user_bom_product_extract.status AS status',
          'user_bom_product_extract.confidence AS confidence',
          'user_bom_product_extract.product_tag AS product_tag',
          'user_bom_product_extract.description AS description',
          'user_bom_product_extract.specification AS specification',
          'user_bom_product_extract.search_string AS search_string',
          'user_bom_product_extract.grade AS grade',
          'user_bom_product_extract.shape AS shape',
          'user_bom_product_extract.qty AS qty',
          'user_bom_product_extract.qty_unit AS qty_unit',
          'user_bom_product_extract.price_per_unit AS price_per_unit',
          'user_bom_product_extract.price_unit AS price_unit',
          'user_bom_product_extract.length AS length',
          'user_bom_product_extract.weight_per_quantity AS weight_per_quantity',
          'user_bom_product_extract.matched_product_count AS matched_product_count',
          'user_bom_product_extract.matched_products AS matched_products',
          'user_bom_product_extract.selected_products AS selected_products',
          'user_bom_product_extract.current_page AS current_page',
          'user_bom_product_extract.total_pages AS total_pages',
          'user_bom_product_extract.product_index AS product_index',
          'user_bom_product_extract.domestic_material_only AS domestic_material_only',
          'user_bom_product_extract.buyer_line_total AS buyer_line_total',
          'user_bom_product_extract.last_updated_product AS last_updated_product',
          'user_bom_product_extract.total_weight AS line_weight',
        ],
      };
      const mappedProducts  = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userBomUploadsRepository,leftJoins,conditions,mapperFields,orderBy);
      const transformedData = mappedProducts.reduce((res, item) => {
        const { bom_upload_id, file_name, attempt, actual_file_name, unique_identifier, current_page, bom_status, s3_url, total_pages, device_id, material_total, sales_tax, total_weight, review_status, line1, line2, city, state_id, zip, title, type, delivery_date, created_date, ...rest } = item;

        const modifiedFileName = attempt > 0 ? `${file_name} Attempt ${attempt}`: file_name;
        const uniqueKey = `${unique_identifier}-${modifiedFileName}`;

        if (!res[uniqueKey]) {
          res[uniqueKey] = {
            id: bom_upload_id,
            file_name:modifiedFileName,
            actual_file_name,
            unique_identifier,
            status: bom_status,
            s3_url,
            total_pages,
            device_id,
            material_total,
            sales_tax,
            total_weight,
            total_purchase : (Number(material_total) + Number(sales_tax)).toFixed(2),
            deposit : (0).toFixed(2),
            subscription : (0).toFixed(2),
            review_status,
            line1,
            line2,
            city,
            state_id,
            zip,
            title,
            type,
            delivery_date,
            created_date,
            result: [],
          };
        }
        
        rest.matched_products = rest.matched_products ? JSON.parse(rest.matched_products) : []; 
        rest.selected_products = rest.selected_products ? JSON.parse(rest.selected_products) : [];
        
        res[uniqueKey].result.push(rest);

        return res;
      }, {});
      if(bomId){
        return Object.values(transformedData)[0];
      }
      return Object.values(transformedData);
    }catch(error){
      BryzosLogger.log(JSON.stringify({ error: error, event: "Get uploaded BOM Error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message : "Something went wrong!!" }
    }
  }

  async saveGameScore(userId: string, payload: GameScore, superAdminUserId: string) {
    try {
      const userData = await this.dbServiceObj.findOne(this.userRepository, "id", userId);
      if (!userData) {
        return { error_message: "User not found" }
      }
      payload["user_id"] = userId;
      payload["super_admin_user_id"] = superAdminUserId;
      payload["company_id"] = userData.company_id;
      await this.dbServiceObj.saveData(payload, this.userGameScoreRepository);

      let conditions = []
      conditions.push({ column: 'is_active', operator: '=', value: true })
  
      const mapperFields = {
        'selectFields': [
          'table1.id AS id',
           'table1.score AS score',
           'table1.user_id AS user_id'
        ],
      };
      const orderBy = { 'table1.created_date': 'DESC' };
      const scoreData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userGameScoreRepository, [], conditions, mapperFields, orderBy);
      const userHighestScores = [...new Set(scoreData.map(s => s.user_id))]
        .map(id => {
          const highestScoreObj = scoreData.filter(s => s.user_id === id)
            .reduce((max, curr) => curr.score > max.score ? curr : max);
          return {
            id: highestScoreObj.id,
            user_id: id,
            score: highestScoreObj.score
          };
        })
        .sort((a, b) => b.score - a.score);

      // Group users by score and assign ranks
      const rankedScores = [];
      let rank = 1;
      let lastScore = null;

      for (const user of userHighestScores) {
        if (lastScore !== null && user.score < lastScore) {
          // New lower score gets next rank
          rank++;
        }
        // Add user with their rank
        rankedScores.push({ ...user, rank });
        lastScore = user.score;
      }
      await this.dbServiceObj.updateByMultipleWhere({"rank":null}, {"is_active":true}, this.userGameScoreRepository);
      await this.dbServiceObj.saveData(rankedScores,this.userGameScoreRepository);
      const payloadData = { // create payload for websocket event of reading progress
        overall_highest_score : rankedScores[0].score,
      }
    
      const url = process.env.GISS_WS_SERVER + "/rankUpdate";
      await this.extLibUtility.sendWebsocketEvent(url,payloadData);
      return "Game score has been saved successfully."
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error: error, event: "BOM save game score error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!!" }
    }
  }

  

  async getGameScore(userId:string){
    try{
      const userData = await this.dbServiceObj.findOne(this.userRepository,"id",userId);
      if(!userData){
        return { error_message : "User not found" }
      }
      const leftJoins = [
        { "table": "user", "joinColumn": "id", "mainTableColumn": "user_id" },
      ];
      let conditions = []
      conditions.push({ column: 'is_active', operator: '=', value: true })
      conditions.push({ column: 'is_active', operator: '=', value: true, table: "user" });
  
     
      
      const mapperFields = {
        'selectFields': [
          'table1.id AS id',
           'table1.score AS score',
           'table1.consecutive_catches AS consecutive_catches',
           'table1.company_id AS company_id',
           'table1.super_admin_id AS super_admin_id',
           'table1.user_id AS user_id',
           'user.first_name AS first_name',
           'user.last_name AS last_name'
        ],
      };
      const orderBy = { 'table1.created_date': 'DESC' };
      const scoreData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userGameScoreRepository, leftJoins, conditions, mapperFields, orderBy);
      const userHighestScoreData = scoreData
        .filter(obj => obj.user_id == userId)
        .reduce((max, obj) => (obj.score > max.score ? obj : max), { score: -Infinity });
  
      // Find overall highest score
      const overallHighestScoreData = scoreData
        .reduce((max, obj) => (obj.score > max.score ? obj : max), { score: -Infinity });
  
      // Find highest score for the company
      const companyHighestScoreData = scoreData
        .filter(obj => obj.company_id == userData.company_id)
        .reduce((max, obj) => (obj.score > max.score ? obj : max), { score: -Infinity });
      
      const userHighestCatchesData = scoreData
        .filter(obj => obj.user_id == userId)
        .reduce((max, obj) => (obj.consecutive_catches > max.consecutive_catches ? obj : max), { consecutive_catches: -Infinity });
  
      // Find overall highest score
      const overallHighestCatchesData = scoreData
        .reduce((max, obj) => (obj.consecutive_catches > max.consecutive_catches ? obj : max), { consecutive_catches: -Infinity });
  
      // Find highest score for the company
      const companyHighestCatchesData = scoreData
        .filter(obj => obj.company_id == userData.company_id)
        .reduce((max, obj) => (obj.consecutive_catches > max.consecutive_catches ? obj : max), { consecutive_catches: -Infinity });
      
      
      // Get the highest score for each user and sort
      const userHighestScores = [...new Set(scoreData.map(s => s.user_id))]
        .map(id => ({ 
          id, 
          score: Math.max(...scoreData.filter(s => s.user_id === id).map(s => s.score))
        }))
        .sort((a, b) => b.score - a.score);
      
      // Group users by score and assign ranks
      const rankedScores = [];
      let rank = 1;
      let lastScore = null;
      
      for (const user of userHighestScores) {
        if (lastScore !== null && user.score < lastScore) {
          // New lower score gets next rank
          rank++;
        }
        // Add user with their rank
        rankedScores.push({ ...user, rank });
        lastScore = user.score;
      }

      // Find the user's rank
      const userRankObj = rankedScores.find(item => item.id == userId);
      const userRank = userRankObj ? userRankObj.rank : 0;
      
      return {
          "user_rank": userRank || 0,
          "user_highest_score": {
            score: userHighestScoreData.score,
            first_name: userHighestScoreData.first_name,
            last_name: userHighestScoreData.last_name,
          },
          "overall_highest_score": {
            score: overallHighestScoreData.score,
            first_name: overallHighestScoreData.first_name,
            last_name: overallHighestScoreData.last_name,
          },
          "company_highest_score": {
            score: companyHighestScoreData.score,
            first_name: companyHighestScoreData.first_name,
            last_name: companyHighestScoreData.last_name,
          },
          "user_highest_consecutive_catches": {
            consecutive_catches: userHighestCatchesData.consecutive_catches,
            first_name: userHighestCatchesData.first_name,
            last_name: userHighestCatchesData.last_name,
          },
          "overall_highest_consecutive_catches": {
            consecutive_catches: overallHighestCatchesData.consecutive_catches,
            first_name: overallHighestCatchesData.first_name,
            last_name: overallHighestCatchesData.last_name,
          },
          "company_highest_consecutive_catches": {
            consecutive_catches: companyHighestCatchesData.consecutive_catches,
            first_name: companyHighestCatchesData.first_name,
            last_name: companyHighestCatchesData.last_name,
          }
      };
    }catch(error){
      BryzosLogger.log(JSON.stringify({ error: error, event: "BOM get game score error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!!" }
    }
  }

  async saveBOMDraftData(payload: BomDraftDto, userId) {
    try {
      let response = null;
      if (payload.status === ExtLibConstants.APPROVED) {
        if (!payload.selected_product_id || !payload.qty) {
          return { error_message: "selected_product_id and qty are required field." };
        }
      }
      const extractedProductsData = await this.dbServiceObj.findOne(this.userBomProductExtractRepository, "id", payload.id);

      if (!extractedProductsData) { return { error_message: "No extracted products were found that you are trying to update." } }

      let saveBomLogs = {};
      let updateDto = {};
      if (payload.status !== extractedProductsData.status) {
        updateDto["status"] = payload.status;
        saveBomLogs["status"] = extractedProductsData.status;
      }

      const selectedProducts = extractedProductsData.selected_products ? JSON.parse(extractedProductsData.selected_products) : null;

      if (payload?.selected_product_id && (!selectedProducts || !selectedProducts.includes(payload.selected_product_id))) {
        updateDto["selected_products"] = JSON.stringify([payload.selected_product_id]);
        saveBomLogs["selected_product_id"] = selectedProducts ? selectedProducts[0] : null;
      }

      if (payload?.qty && payload.qty !== extractedProductsData.qty) {
        updateDto["qty"] = payload.qty;
        saveBomLogs["qty"] = extractedProductsData.qty;
      }

      if (payload.qty_unit !== extractedProductsData.qty_unit) {
        updateDto["qty_unit"] = payload.qty_unit;
        saveBomLogs["qty_unit"] = extractedProductsData.qty_unit;
      }

      if (payload.product_tag !== extractedProductsData.product_tag) {
        updateDto["product_tag"] = payload.product_tag;
        saveBomLogs["product_tag"] = extractedProductsData.product_tag;
      }

      if ( payload.domestic_material_only !== extractedProductsData.domestic_material_only) {
        updateDto["domestic_material_only"] = payload.domestic_material_only;
        saveBomLogs["domestic_material_only"] = extractedProductsData.domestic_material_only;
      }


      if (Object.keys(updateDto).length > 0) {
        await this.dbServiceObj.updateWithoutMapper({ 'last_updated_product': false }, 'bom_upload_id', extractedProductsData.bom_upload_id, this.userBomProductExtractRepository);
        updateDto['id'] = payload.id;
        updateDto['last_updated_product'] = true;
        await this.dbServiceObj.saveWithOutMapper(updateDto, userId, this.userBomProductExtractRepository);
        response = "Draft Saved Successfully!";
      }

      if (Object.keys(saveBomLogs).length > 0) {
        saveBomLogs['product_extraction_id'] = payload.id;
        await this.dbServiceObj.saveWithOutMapper(saveBomLogs, userId, this.bomProductExtractionUpdateLogsRepository);
      }

      return response;
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error: error, event: "BOM save draft data error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!!" }
    }
  }

  async saveBOMSummaryData(payload: BomSummaryDto, userId) {
    try {
      let response = null;
      const id = payload.bom_upload_id;
      let errorMessage = [];
      const userData = await this.dbServiceObj.findOne(this.userRepository, "id", userId);
      // get upload bom details 
      const bomUploadData = await this.dbServiceObj.findOneByMultipleWhere(this.userBomUploadsRepository, {"id": id, "user_id": userId, "review_status": ExtLibConstants.PENDING_REVIEW});
      if(!bomUploadData){ return { error_message: "BOM upload data not found" } }
      
      // check user have spread settings ON
      const checkUserHaveSpread = await this.dbServiceObj.findOneByMultipleWhere(this.userRepository,{"id": userId,"is_buyer_spread":true});
      let buyerSpreadRate = 1;
      let spreadPricingColumn = null;
      if(checkUserHaveSpread){ 
        buyerSpreadRate = checkUserHaveSpread.buyer_spread_rate;
        spreadPricingColumn = checkUserHaveSpread.base_pricing_column.toLowerCase();
      };
      
      // get extracted products data order by current_page and product_index
      const extractedProductsDataConditions = [
        { columnName: 'bom_upload_id', operator: '=', value: id },
        { columnName: 'is_active', operator: '=', value: true },
      ]
      const orderBy = { 'current_page': 'ASC', 'product_index': 'ASC' };
      const extractedProductsData = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.userBomProductExtractRepository, extractedProductsDataConditions,"*",orderBy);
      if (extractedProductsData.length === 0) { return { error_message: "No extracted products were found that you are trying to update." } }
      let pendingReviewBomIds = [];
      extractedProductsData.forEach(item => {
        if(item.status === ExtLibConstants.PENDING){
          pendingReviewBomIds.push(item.id);
        }
      });
      if(pendingReviewBomIds.length > 0){
        return { error_message: 'Review is pending for this BOM. Please wait until all lines are reviewed.',
          pending_review_bom_ids: pendingReviewBomIds
        }
      }
      // make an array of selected product ids from extracted products data
      let productIds = [];
      extractedProductsData.forEach(item => {
        if(item.selected_products && item.status === ExtLibConstants.APPROVED){
          const selectedProducts = JSON.parse(item.selected_products);
          productIds = [...productIds, ...selectedProducts];
        }
      });
      // Remove duplicates if any
      productIds = [...new Set(productIds)];
      if(productIds.length === 0){ return { error_message : "Product not found in review!" } }

      // get reference product details
      let referenceProductDetails = await this.dbServiceObj.findManyByWhereIn(this.referenceDataProductsWidgetRepository,"Product_ID", productIds);
      referenceProductDetails = referenceProductDetails.filter(item => item.is_safe_product_code !== 1);
      if(!referenceProductDetails || referenceProductDetails.length === 0 ){ return { error_message : "Product not found!" } }
      // convert reference product object keys to lowercase
      referenceProductDetails = referenceProductDetails.map(obj =>
        Object.fromEntries(
          Object.entries(obj).map(([key, value]) => [key.toLowerCase(), value])
        )
      );

      const filteredProductIds = referenceProductDetails.map(item => item.product_id);

      let totalBuyerPrice:number = 0;
      let totalLinesWeight:number = 0;
      const updateDtoArray = [];
      let unitErrors = "";
      let qtyErrors = "";
      let line = 0;
      let salesTaxRate = 0;
      let totalSalesTax:number = 0;
      const resaleCertConditions = {
        user_id : userId,
        state_id  : bomUploadData.state_id
      }

      const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userResaleCertificateRepository,resaleCertConditions);
      const getSalesTaxData = await this.baseLibraryService.calculateSalesTaxRate(bomUploadData.state_id,bomUploadData.zip,resaleCertData,this.userPurchaseOrderRepository);
      if(getSalesTaxData){ 
        salesTaxRate = getSalesTaxData.sales_tax_rate; 
      }

      // loop through extracted products data
      const orderSize = await this.getBomOrderSizeOneReviewComplete(extractedProductsData, referenceProductDetails);

      if(!orderSize){
        return { error_message : "Something went wrong while calculating order size" };
      }

      let zipcode = bomUploadData.zip;
      if(!zipcode){
          return { error_message : "Zipcode or order size is required for pricing" };
      }

      // function to fetch volumn region pricing
      let volumeRegionPricing = await this.orderUtilityLibrary.getVolumeRegionPricing(filteredProductIds, zipcode, Number(orderSize));

      if(volumeRegionPricing.hasOwnProperty('error_message')){
          return volumeRegionPricing;
      }

      for (const extractedProduct of extractedProductsData) {
        line++;
        // Check if the status is "DELETED"
        if (extractedProduct && extractedProduct.status === ExtLibConstants.DELETED) {
          const updateDto = {
            id: extractedProduct.id,
            is_active: false
          };
          updateDtoArray.push(updateDto);
        }
        
        //Check if the status is "APPROVED"
        if(extractedProduct.status === ExtLibConstants.APPROVED){

          const productId = JSON.parse(extractedProduct['matched_products'])[0];
          const pricingDetails =  volumeRegionPricing[productId]['prices'];

          const selectedProductId = JSON.parse(extractedProduct.selected_products)[0];
          let productDetails = referenceProductDetails.find( obj => obj.product_id === selectedProductId ); 
          let qumDropdownOptions = productDetails.qum_dropdown_options.toLowerCase().split(",");

          if(!qumDropdownOptions.includes(extractedProduct.qty_unit.toLowerCase())){
            errorMessage.push({"ln" : line, "error": ['qty_unit']});
            continue;
          }

          let qtyUnit = extractedProduct.qty_unit.split(" ").length > 1 ? extractedProduct.qty_unit.replace(/ /g,"_").toLowerCase() : extractedProduct.qty_unit.toLowerCase();

          let qty = +extractedProduct.qty;
          qtyUnit = qumDropdownOptions.includes(qtyUnit.replace(/_/g, ' ').toLowerCase()) ? qtyUnit : null;

          let validateQty = false;
          let orderIncrementColumn = null;
          if(qtyUnit != null){
              orderIncrementColumn = qtyUnit.toLowerCase() == "pc" ? "order_increment_ea" : "order_increment_"+qtyUnit;
              validateQty = await this.orderUtilityLibrary.validateCheckoutItemsQty(extractedProduct.qty,productDetails[`${orderIncrementColumn}`]);
          }
          
          if(validateQty === false){
            errorMessage.push({"ln" : line, "error": ['qty']});
            continue;
          }

          // let buyerCalculationPriceColumn = spreadPricingColumn != null ? spreadPricingColumn+"_" : 'buyer_pricing_';
          // buyerCalculationPriceColumn = buyerCalculationPriceColumn+qtyUnit;
          // let buyerCalculationPrice = productDetails?.[`${buyerCalculationPriceColumn}`] ?? 0;
          // let formattedbuyerCalculationPrice:number = parseFloat(buyerCalculationPrice.replace(/,/g, '').substring(1));
          // buyerCalculationPrice = buyerSpreadRate === 1 ? formattedbuyerCalculationPrice : formattedbuyerCalculationPrice * +buyerSpreadRate;
          // buyerCalculationPrice = qtyUnit == 'lb' ? +buyerCalculationPrice.toFixed(4) : +buyerCalculationPrice.toFixed(2);
          let buyerCalculationPrice = pricingDetails[qtyUnit];
          buyerCalculationPrice = buyerSpreadRate === 1 ? buyerCalculationPrice : buyerCalculationPrice * +buyerSpreadRate;
          buyerCalculationPrice = qtyUnit == 'lb' ? +buyerCalculationPrice.toFixed(4) : +buyerCalculationPrice.toFixed(2);

          //Buyer line total price
          let buyerExtended:number = buyerCalculationPrice * qty;
          buyerExtended = Number(buyerExtended.toFixed(2));

          //Buyer calculation price per unit without spread
          // let withoutSpreadBuyerCalculationPriceColumn =  "buyer_pricing_"+qtyUnit;
          // let withoutSpreadBuyerCalculationPrice = productDetails[`${withoutSpreadBuyerCalculationPriceColumn}`] ?? 0;
          // let formattedWithoutSpreadBuyerCalculationPrice:number = await this.sanatizeReferenceDataProductsamount(withoutSpreadBuyerCalculationPrice);


          //Buyer line total without spread
          // let buyerExtendedWithoutSpread = formattedWithoutSpreadBuyerCalculationPrice * +qty;
          // let buyerPricingLb:number = await this.sanatizeReferenceDataProductsamount(productDetails?.buyer_pricing_lb ?? 0);
          const lbsPerFt = productDetails['lbs_ft'] ?? 0;
          const orderIncrementFt = productDetails['order_increment_ft'] ?? 0;
          let lineTotalWeight: number = await this.orderUtilityLibrary.calculateLineWeightByFormula(qty, { LBS_FT: lbsPerFt, Order_Increment_Per_Unit: productDetails[`${orderIncrementColumn}`], Order_Increment_Ft: orderIncrementFt });
            
          let salesTax = 0;
          let unformattedSalesTax = salesTaxRate * buyerExtended;
          salesTax = parseFloat(unformattedSalesTax.toFixed(2));
          
          const updateDto = {
            id: extractedProduct.id,
            buyer_line_total: buyerExtended,
            price_per_unit: buyerCalculationPrice,
            price_unit: extractedProduct.qty_unit,
            shape: productDetails.key2,
            total_weight: lineTotalWeight,
            sales_tax: salesTax
          };
          updateDtoArray.push(updateDto);
          totalBuyerPrice += buyerExtended;
          totalLinesWeight += lineTotalWeight;
          totalSalesTax += salesTax;
        }
      }

      if(errorMessage.length > 0){
        return { error_message: errorMessage }
      }

      if (updateDtoArray.length > 0 && totalBuyerPrice > 0) {
        await this.dbServiceObj.saveWithOutMapper(updateDtoArray, userId, this.userBomProductExtractRepository);
        const bomReviewUpdateDto = {
          id: bomUploadData.id,
          buyer_po_price: totalBuyerPrice,
          total_weight: totalLinesWeight,
          sales_tax: totalSalesTax,
          review_status: ExtLibConstants.COMPLETED
        }
        await this.dbServiceObj.saveData(bomReviewUpdateDto, this.userBomUploadsRepository);
        const payloadData = { // create payload for websocket event of reading progress
          bom_upload_id : bomUploadData.id,
          email_id : userData.email_id
        }
      
        const url = process.env.GISS_WS_SERVER + "/bomReviewCompleted";
        await this.extLibUtility.sendWebsocketEvent(url,payloadData);
        return "BOM Summary data has been saved successfully!";
      }else{
        return { error_message: "BOM Summary data has not been saved successfully!" }
      }
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error: error, event: "BOM save review complete data error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!!" }
    }
  }

  async getBomOrderSizeOneReviewComplete(extractedProductsData: any, referenceProductDetails: any) {
    let orderSize = 0;
    try {
      for (const extractedProduct of extractedProductsData) {
        //Check if the status is "APPROVED"
        if(extractedProduct.status !== ExtLibConstants.APPROVED){ continue; } 

        let qtyUnit = extractedProduct.qty_unit.split(" ").length > 1 ? extractedProduct.qty_unit.replace(/ /g,"_").toLowerCase() : extractedProduct.qty_unit.toLowerCase();
        let qty = +extractedProduct.qty;

        const selectedProductId = JSON.parse(extractedProduct.selected_products)[0];
        let productDetails = referenceProductDetails.find( obj => obj.product_id === selectedProductId ); 
        const lbsPerFt = productDetails['lbs_ft'] ?? 0;

        let qumDropdownOptions = productDetails.qum_dropdown_options.toLowerCase().split(",");
        qumDropdownOptions.push("pc");
        qtyUnit = qumDropdownOptions.includes(qtyUnit.replace(/_/g, ' ').toLowerCase()) ? qtyUnit : null;

        let validateQty = false;
        let orderIncrementColumn = null;
        if(qtyUnit != null){
            orderIncrementColumn = qtyUnit.toLowerCase() == "pc" ? "order_increment_ea" : "order_increment_"+qtyUnit;
            validateQty = await this.orderUtilityLibrary.validateCheckoutItemsQty(extractedProduct.qty,productDetails[`${orderIncrementColumn}`]);
        }
        
        if(validateQty === false){
          return false;
        }
        
        const orderIncrementFt = productDetails['order_increment_ft'] ?? 0;
        
        let lineTotalWeight: number = await this.orderUtilityLibrary.calculateLineWeightByFormula(qty, { LBS_FT: lbsPerFt, Order_Increment_Per_Unit: productDetails[`${orderIncrementColumn}`], Order_Increment_Ft: orderIncrementFt });
        orderSize += lineTotalWeight;
      }
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error: error, event: "BOM save review complete order size data error" }), process.env.LOGGLY_ERROR_TAG);
      return false;
    }
    return orderSize;
  }

  // async uploadFileToParser(bomUploadId: string) {
  //   try {
  //     const userBomupload = await this.dbServiceObj.findOneByMultipleWhere(this.userBomUploadsRepository, { "id": bomUploadId, "is_file_uploaded_to_parser": false });
  //     if (!userBomupload) { return { error_message: "File not found to upload to parser" } }
  //     const originalFileName = userBomupload.original_file_name;
  //     const uniqueIdentifier = userBomupload.unique_identifier;
  //     // Get S3 file path
  //     const s3Path = userBomupload.s3_url;
  //     if (!s3Path) {
  //       return { error_message: "S3 URL is required" };
  //     }
      
  //     // Parse S3 URL (format: https://bucket-name.s3.amazonaws.com/key)
  //     const s3UrlMatch1 = s3Path.match(/^https:\/\/([^.]+)\.s3\.amazonaws\.com\/(.+)$/);
  //     const s3UrlMatch2 = s3Path.match(/^https:\/\/([^.]+)\.s3\.([^.]+)\.amazonaws\.com\/(.+)$/);
  //     if (!s3UrlMatch1 && !s3UrlMatch2) { return { error_message: "Invalid S3 URL format" } }

  //     // Extract bucket and key
  //     let bucket: string;
  //     let key: string;
      
  //     if (s3UrlMatch1) {
  //       [, bucket, key] = s3UrlMatch1;
  //     } else {
  //       [, bucket, , key] = s3UrlMatch2; // Skip the region part
  //     }
  //     key = decodeURIComponent(key);
  //     const fileName = uniqueIdentifier+"_"+originalFileName;
  //     // Download file from S3
  //     const s3FileStream = await this.awsUtility.getS3File(key, bucket);

  //     await this.extLibUtility.makeStorageFolder();
  //     const tempFilePath = `./storage/${fileName}`;
  //     await this.extLibUtility.savePdfToFile(tempFilePath, s3FileStream);

  //     // Read file for upload
  //     const fileBuffer = await fs.readFile(tempFilePath);

  //     // Set up API config based on parser ID
  //     const parserId = userBomupload.parser_id;
  //     let token: string;
  //     let url: string;
  //     let headers ;
  //     const formData = new FormData();
  //     formData.append('file', new Blob([fileBuffer]), fileName);
     
  //     if (parserId === 1) {
  //       // Parsio API
  //       token = process.env.PARSIO_API_KEY;
  //       url = `${process.env.PARSIO_URL}/upload`;
  //       headers = { 'X-API-Key': token };
  //     } else if (parserId === 2) {
  //       // Parseur API
  //       token = process.env.PARSEUR_TOKEN;
  //       url = `${process.env.PARSEUR_URL}/upload`;
  //       headers = {
  //         'Authorization': `Token ${token}`,
  //         'Content-Type': 'multipart/form-data'
  //       };
  //     }else{
  //       throw new HttpException('Invalid parser ID', HttpStatus.BAD_REQUEST);
  //     }

  //     // Send to parser
  //     const response = await axios.post(url, formData, { headers });

  //     // Cleanup and update status
  //     await fs.unlink(tempFilePath);

  //     // Mark as email sent to parser
  //     await this.dbServiceObj.updateByColumnId( this.userBomUploadsRepository, { is_file_uploaded_to_parser: true }, 'id', bomUploadId );

  //     return response.data;
  //   } catch (error) {
  //     return {
  //       error_message: error.response?.data?.non_field_errors || error.response?.data?.message || "Error in uploading file to parser"
  //     };
  //   }
  // }

  async saveBomHeaderDetails(payload: BomHeaderDetailsDto) {
    // validate state id and zip code
    const shippingDetails = payload.shipping_details;
    let stateId = shippingDetails.state_id;
    const zipCode = shippingDetails.zip;
    const line1 = shippingDetails.line1;
    const line2 = shippingDetails.line2;
    const city = shippingDetails.city;
    const deliveryDate = await this.getDeliveryDate(payload.delivery_date);
    
    const validateStateZip = await this.orderUtilityLibrary.matchStateZipCode(stateId, zipCode);
    if(!validateStateZip){ return { error_message: "Invalid state or zip code" }; }

    const updateDto = {
      id: payload.bom_upload_id,
      bom_name: payload.bom_name,
      bom_type: payload.bom_type,
      state_id: validateStateZip,
      zip: zipCode,
      line1: line1,
      line2: line2,
      city: city,
      delivery_date: deliveryDate
    }

    await this.dbServiceObj.saveData(updateDto, this.userBomUploadsRepository);
    return "BOM header details saved successfully";
  }

  async sanatizeReferenceDataProductsamount(price: string) {
    return parseFloat(price.replace(/,/g, '').substring(1));
  }

  async getLineWeight(buyerPricingLb,buyerExtended){
    return  buyerPricingLb && buyerExtended ? +buyerExtended / +buyerPricingLb : 0;
  }

  async getDeliveryDate(date: string) {
    const formatedDate = format(new Date(date), 'MM/dd/yyyy');
    return await this.baseLibUtility.converToMsqlDatetime(formatedDate);
  }

  async getCombinedBomAndDraftPoData(userId: string, bomId?: string) {
    try {
      
      const savedBOMData = await this.getSavedBom(userId, bomId);

      const draftPoData = await this.getDraftPoData(userId);

      if (savedBOMData && savedBOMData['error_message']) {
        return savedBOMData;
      }

      if (draftPoData && draftPoData['error_message']) {
        return draftPoData;
      }

      const normalizedBom = Array.isArray(savedBOMData)
        ? savedBOMData.map(bom => ({
          ...bom,
          is_draft_po: false,
          __parsed_created_date: Date.parse(bom.created_date) || 0
        }))
        : (savedBOMData && typeof savedBOMData === 'object' && !Array.isArray(savedBOMData))
          ? [{
            ...(savedBOMData as Record<string, any>),
            is_draft_po: false,
            __parsed_created_date: Date.parse((savedBOMData as any).created_date) || 0
          }]
          : [];

      const normalizedDrafts = Array.isArray(draftPoData) ? (draftPoData || []).map(draftPo => ({
        ...draftPo,
        is_draft_po: true,
        __parsed_created_date: Date.parse(draftPo.created_date) || 0
      })) : [];

      const combinedData = [...normalizedBom, ...normalizedDrafts];
    
      // Efficient sort by parsed date (descending)
      combinedData.sort((a, b) => b.__parsed_created_date - a.__parsed_created_date);

      // Clean up helper field before returning
      const finalData = combinedData.map(({ __parsed_created_date, ...rest }) => rest);

      return finalData;

    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error, event: "Get combined BOM and PO draft Data error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!" };
    }
  }

  async getDraftPoData(userId: string) {
    try {
      let conditions = [];
      conditions.push({ column: "is_active", operator: "=", value: true }, { column: "is_active", operator: "=", value: true, table: "save_order_draft_lines" });
      conditions.push({ column: "buyer_id", operator: "=", value: userId })
      const leftJoin = [
        { "table": "save_order_draft_lines", "joinColumn": "draft_id", "mainTableColumn": "id" }
      ];
      const mapperFields = {
        selectFields: [
          'table1.id as draft_id',
          'table1.buyer_id as buyer_id',
          'table1.buyer_internal_po as title',
          'table1.payment_method as payment_method',
          'table1.line1 as line1',
          'table1.line2 as line2',
          'table1.city as city',
          'table1.zip as zip',
          'table1.state_id as state_id',
          'table1.freight_term as freight_term',
          'table1.total_weight as total_weight',
          'table1.buyer_price as material_total',
          'table1.sales_tax as sales_tax',
          'table1.deposit_amount as deposit',
          'table1.order_type as type',
          'DATE_FORMAT(CONVERT_TZ(table1.delivery_date,"UTC","America/Chicago"), "%m/%d/%Y %h:%i %p") AS delivery_date',
          'table1.created_date as created_date',
          'table1.time_stamp as time_stamp',
          'save_order_draft_lines.id as id',
          'save_order_draft_lines.po_line as line_id',
          'save_order_draft_lines.description as description',
          'save_order_draft_lines.qty as qty',
          'save_order_draft_lines.qty_unit as qty_unit',
          'save_order_draft_lines.product_tag as product_tag',
          'save_order_draft_lines.buyer_price_per_unit as price_per_unit',
          'save_order_draft_lines.price_unit as  price_unit',
          'save_order_draft_lines.buyer_line_total as buyer_line_total',
          'save_order_draft_lines.product_id as product_id',
          'save_order_draft_lines.reference_product_id as reference_product_id',
          'save_order_draft_lines.shape as shape',
          'save_order_draft_lines.total_weight as line_weight',
          'save_order_draft_lines.product_id as product_id',
          'save_order_draft_lines.reference_product_id as reference_product_id',
          'save_order_draft_lines.buyer_pricing_lb as buyer_pricing_lb',
          'save_order_draft_lines.sales_tax as sales_tax',
          'save_order_draft_lines.domestic_material_only as domestic_material_only'
        ]
      }
      const orderBy = {
        'table1.created_date': 'DESC',
        'save_order_draft_lines.po_line': 'ASC'
      };
      
      let orderDraft = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.saveOrderDraftRepository, leftJoin, conditions, mapperFields, orderBy);

      const response = [];
      const accumulator = {}; // This will be used to store grouped items by draft_id

      // Loop through each item in the orderDraft array using for...of
      for (const item of orderDraft) {
        const key = item.draft_id; // Create a unique key based on draft_id

        // If the key doesn't exist in the accumulator, initialize it
        if (!accumulator[key]) {
          accumulator[key] = {
            id: key,
            buyer_id: item.buyer_id,
            title: item.title,
            payment_method: item.payment_method,
            deposit: item.deposit,
            line1: item.line1,
            line2: item.line2,
            city: item.city,
            zip: item.zip,
            state_id: item.state_id ? Number(item.state_id) : null,
            freight_term: item.freight_term,
            total_weight: item.total_weight,
            material_total: item.material_total,
            sales_tax: item.sales_tax,
            type: item.type,
            delivery_date: item.delivery_date,
            created_date: item.created_date,
            result: []
          };
        }

        // Push the current item into the result array
        accumulator[key].result.push({
          id: item.id,
          line_id: item.line_id,
          description: item.description,
          qty: item.qty,
          qty_unit: item.qty_unit,
          product_tag: item.product_tag,
          price_per_unit: item.price_per_unit,
          price_unit: item.price_unit,
          buyer_line_total: item.buyer_line_total,
          product_id: item.product_id,
          reference_product_id: item.reference_product_id,
          shape: item.shape,
          line_weight: item.line_weight,
          buyer_pricing_lb: item.buyer_pricing_lb,
          sales_tax: item.sales_tax,
          domestic_material_only: item.domestic_material_only
        });
      }

      // Convert the accumulator object to an array of values (same as Object.values)
      for (const key in accumulator) {
        if (accumulator.hasOwnProperty(key)) {
          response.push(accumulator[key]);
        }
      }

      return response;
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error: error, event: "Get draft po data Error" }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: "Something went wrong!!" }
    }
  }

  async textExtractFromFile(payload:BomUpload, userId:string) {
    try{

      let fileName = payload.actual_file_name;
      let deviceId = payload?.device_id;
      const deliveryDate = await this.getDeliveryDate(payload.delivery_date);
      const validateStateZip = await this.orderUtilityLibrary.matchStateZipCode(payload.shipping_details.state_id, payload.shipping_details.zip);
      if(!validateStateZip){ return { error_message: "Please enter a valid zip code and state." }; }

      const insertDto = {
        user_id : userId,
        original_file_name : fileName,
        s3_url : payload.s3_url,
        device_id: deviceId,
        status : ExtLibConstants.INPROGRESS,
        review_status: ExtLibConstants.PENDING_REVIEW, 
        bom_name: payload.bom_name,
        bom_type: payload.bom_type,
        state_id: validateStateZip,
        zip: payload.shipping_details.zip,
        line1: payload.shipping_details.line1,
        line2: payload.shipping_details.line2,
        city: payload.shipping_details.city,
        delivery_date: deliveryDate
      }
      const saveBomData = await this.dbServiceObj.saveData(insertDto,this.userBomUploadsRepository);
      const saveBomDataId = saveBomData.id;
      let s3ObjectKey = payload.object_key;
      const startResult = await this.awstextract.startAnalysis(s3ObjectKey);

      // Check if starting analysis was successful
      if (!startResult.success) {
        this.dbServiceObj.updateWithoutMapper({ is_active: false, json_data: JSON.stringify(startResult), status: ExtLibConstants.FAILED }, 'id', saveBomDataId, this.userBomUploadsRepository);
        return {
          error_message: startResult.message,
          error_code: startResult.error,
          details: startResult.details
        };
      }

      // Get analysis results
      const results = await this.awstextract.getAnalysisResults(startResult.jobId);

      // Check if getting results was successful
      if (!results.success) {
        this.dbServiceObj.updateWithoutMapper({ "is_active": false, json_data: JSON.stringify(results), status: ExtLibConstants.FAILED }, 'id', saveBomDataId, this.userBomUploadsRepository);
        return {
          error_message: (results as any).message,
          error_code: (results as any).error,
          details: (results as any).details
        };
      }

      this.dbServiceObj.updateWithoutMapper({ json_data: JSON.stringify((results as any).Blocks), status: ExtLibConstants.COMPLETED }, 'id', saveBomDataId, this.userBomUploadsRepository);
      return {
        bom_upload_id: saveBomDataId,
        DocumentMetadata: (results as any).DocumentMetadata,
        Blocks: (results as any).Blocks
      };
    }catch(error) {
      console.log('Unexpected error in textExtractFromFile:', error);
      return {
        error_message: "An unexpected error occurred during text extraction",
        error_code: "UNEXPECTED_ERROR",
        details: error.message || "Unknown error"
      };
    }
  }

  async saveProductExtract(payload: BomUploadResultDto, userId: string) {

  }
}
