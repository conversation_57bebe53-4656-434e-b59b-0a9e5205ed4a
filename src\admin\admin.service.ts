import { Injectable } from '@nestjs/common';
import { CognitoAuthService, DataBaseService } from '@bryzos/base-library';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '@bryzos/extended-widget-library';
import { Repository } from 'typeorm';

@Injectable()
export class AdminService {
  private dbServiceObj = new DataBaseService();
  constructor(
    private readonly cognitoAuthService:CognitoAuthService,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
  ) { }


  async getUserAccessToken(userUniqueKey: string) {
    let response = null;
    //check if user exists in our DB
   
    let user = await this.dbServiceObj.findOne(this.userRepository, 'unique_key', userUniqueKey);

    if(user === undefined) {  return { "error_message": "User not found" }; }
    try {
      //get user's access token by email id
      response = await this.cognitoAuthService.authenticateUserWithCustomAuth(user.email_id);
    } catch (error) {
      response = {'error_message': 'Error authenticating user'};
    }
    return response;

  }

  async getAllUsers(userId?: string) {
    let response = null;
    let userData = null;
    const leftJoins = [
      { "table": "user_main_company", "joinColumn": "id", "mainTableColumn": "company_id" },
    ];
    let conditions = []
    conditions.push({ column: 'is_active', operator: '=', value: true })
    conditions.push({ column: 'is_super_admin', operator: '=', value: false });
    conditions.push({ column: 'is_active', operator: '=', value: true, table: "user_main_company" });

    if (userId != null || userId != undefined) {
      conditions.push({ column: "id", operator: "!=", value: userId });
    }
    
    const mapperFields = {
      'selectFields': [
        'table1.id AS id',
        'table1.email_id AS email_id',
        'table1.first_name AS first_name',
        'table1.last_name AS last_name',
        'table1.unique_key AS unique_key',
        'user_main_company.company_name AS company_name'
      ],
    };
    const orderBy = { 'table1.created_date': 'DESC' };
    userData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userRepository, leftJoins, conditions, mapperFields, orderBy);
    if (userData.length > 0) {
      response = userData
    }
    return response;
  }
}
