import { AwsUtilityV3, BaseLibraryService, CacheManagerService, Constants, DataBaseService, ReferenceDataSettings} from '@bryzos/base-library';
import { ReferenceDataBryzosTermsConditions, ReferenceDataCassDisbursementMethod, ReferenceDataDeliveryDate, ReferenceDataGeneralSettings, ReferenceDataPGPMMapping, ReferenceDataProductsWidget, ReferenceDataResaleCertExpiration, ReferenceDataStates, ReferenceEmailEvent, ReferenceDataProductsPreview, ReferenceDataProductsPrevious, AdminLogReferenceDataProductRevert,ReferenceDataProductsLogVersion, ReferenceDataProductsHomepage, AdminLogNotification,ReferenceDataDesktopNotification, HomepageSafeConfig, BryzosLogger, ReferenceDataVideoLibraryTag, ReferenceDataProductsExclusion, User } from '@bryzos/extended-widget-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Constants as LocalConstants} from "src/Constants";
import { AwsQueue } from 'src/AwsQueue';
import { createHash } from 'crypto';
const zlib = require('zlib');
const axios = require('axios');

const payloadTag = Constants.PAYLOAD_TAG;
const responseErrorTag = LocalConstants.ERROR_TAG;

@Injectable()
export class ReferenceDataService {
  private dbObj = new DataBaseService();
  private cacheManager = new CacheManagerService(process.env.CACHE_SERVER, process.env.MEMCACHED_ENDPOINT);
  private productData:any;
 
  constructor(private readonly sm:BaseLibraryService,
  private readonly awsUtility:AwsUtilityV3,
  private readonly awsQueue:AwsQueue,

  @InjectRepository(ReferenceDataProductsWidget) private readonly refProductsWidget: Repository<ReferenceDataProductsWidget>,
  @InjectRepository(ReferenceDataProductsPreview) private readonly refProductsPreview: Repository<ReferenceDataProductsPreview>,
  @InjectRepository(ReferenceDataProductsPrevious) private readonly refProductsPrevious: Repository<ReferenceDataProductsPrevious>,
  @InjectRepository(ReferenceDataBryzosTermsConditions) private readonly refBryzosTermsCondition: Repository<ReferenceDataBryzosTermsConditions>,
  @InjectRepository(ReferenceDataStates) private readonly refStates: Repository<ReferenceDataStates>,
  @InjectRepository(ReferenceDataGeneralSettings) private readonly refGeneralSettings: Repository<ReferenceDataGeneralSettings>,
  @InjectRepository(ReferenceDataPGPMMapping) private readonly pgpmMapping: Repository<ReferenceDataPGPMMapping>,
  @InjectRepository(ReferenceDataDeliveryDate) private readonly deliveryDate: Repository<ReferenceDataDeliveryDate>,
  @InjectRepository(ReferenceDataResaleCertExpiration) private readonly resaleCertExpiration: Repository<ReferenceDataResaleCertExpiration>,
  @InjectRepository(ReferenceEmailEvent) private readonly referenceEmailEvent: Repository<ReferenceEmailEvent>,
  @InjectRepository(ReferenceDataCassDisbursementMethod) private readonly refDataCassDisbursementMethod: Repository<ReferenceDataCassDisbursementMethod>,
  @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettings: Repository<ReferenceDataSettings>,
  @InjectRepository(AdminLogReferenceDataProductRevert) private readonly adminLogReferenceDataProductRevert: Repository<AdminLogReferenceDataProductRevert>,
  @InjectRepository(ReferenceDataProductsLogVersion) private readonly referenceDataProductsLogVersion: Repository<ReferenceDataProductsLogVersion>,
  @InjectRepository(AdminLogNotification) private readonly adminLogNotificationRepository: Repository<AdminLogNotification>,
  @InjectRepository(ReferenceDataProductsHomepage) private readonly referenceDataProductsHomepageRepository: Repository<ReferenceDataProductsHomepage>,
  @InjectRepository(ReferenceDataDesktopNotification) private readonly referenceDataDesktopNotificationRepository: Repository<ReferenceDataDesktopNotification>,
  @InjectRepository(HomepageSafeConfig) private readonly homepageSafeConfigRepository: Repository<HomepageSafeConfig>,
  @InjectRepository(ReferenceDataVideoLibraryTag) private readonly referenceDataVideoLibraryTagRepository: Repository<ReferenceDataVideoLibraryTag>,
  @InjectRepository(ReferenceDataProductsExclusion) private readonly referenceDataProductsExclusionRepository: Repository<ReferenceDataProductsExclusion>,
  @InjectRepository(User) private readonly userRepository: Repository<User>,

  ) 
  {
    this.findAllProductsData();
  }

  async findAllProductsData() {
    this.productData = await this.dbObj.findAll(this.refProductsWidget);
  }

  async findAllProducts() {
    let response = null;
    let refProducts = await this.dbObj.findAll(this.refProductsWidget);
    if (refProducts.length > 0) {
      response = refProducts
    }
    return response;
  }

  async getPrice() {
    //TODO: Once we have external pricing system in place, we need to log price per product and user id in DB before sending it to UI
    //Ex: product_pricing_log => product_id, product_description, price, price_ft, price_lb user_id
    let random = await this.randomInteger(10, 100);
    let random2 = await this.randomInteger(10, 15);
    let roundedNum = (random / random2).toFixed(2);

    let data = {
      "price_ft": random,
      "price_lb": roundedNum
    }
    return data
  }

  async randomInteger(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  async findAll() {

   let cacheData = await this.getCache();
    if(cacheData !== null) {
      return cacheData;
    }
   
    let bryzosTermsConditions = await this.dbObj.findManyWithOrComparision(this.refBryzosTermsCondition,"is_active","type",[Constants.BUYER,Constants.SELLER]);
    let refStates = await this.dbObj.findAllAscOrder(this.refStates);
    let refPgpmMapping = await this.dbObj.findAll(this.pgpmMapping);
    let refdeliveryDate = await this.dbObj.findAll(this.deliveryDate);
    let refresaleCertExpiration = await this.dbObj.findAll(this.resaleCertExpiration);
    let refGeneralSettings = await this.dbObj.findAll(this.refGeneralSettings);
    let domesticMaterialSetting = await this.dbObj.findOne(this.refGeneralSettings, 'name', Constants.DOMESTIC_MATERIAL_ONLY);
    let refDataCassDisbursementMethod = await this.dbObj.findAll(this.refDataCassDisbursementMethod);
    let domesticMaterial = null;
    let accountRoutingNumber = [];   

    const conditions = [Constants.ACCOUNT_NUMBER,Constants.ROUTING_NUMBER];
   
    let account_data = await this.dbObj.findManyByWhereIn(this.referenceDataSettings,"name",conditions);                         
    let account_number = null;
    let routing_number = null;

    for (const row of account_data) {
      if (row.name ===  Constants.ACCOUNT_NUMBER) {
        account_number = row.value;
      } else if (row.name === Constants.ROUTING_NUMBER) {
        routing_number = row.value;
      }
    }

    accountRoutingNumber.push({account_number: account_number, routing_number : routing_number}); 

    if(domesticMaterialSetting != undefined) {
      domesticMaterial = domesticMaterialSetting.value === null ? null : domesticMaterialSetting.value === "true" ? true : false;
    }
    let referenceEmailEvents = await this.dbObj.findAll(this.referenceEmailEvent);

    let response = {
      "ref_bryzos_terms_conditions": bryzosTermsConditions,
      "ref_states": refStates,
      "ref_pgpm_mapping": refPgpmMapping,
      "ref_delivery_date": refdeliveryDate,
      "ref_resale_cert_expiration": refresaleCertExpiration,
      "ref_general_settings": refGeneralSettings,
      "domestic_material_only": domesticMaterial,
      "reference_email_events": referenceEmailEvents,
      "ref_account_routing_number": accountRoutingNumber, 
      "reference_cass_disbursement_method": refDataCassDisbursementMethod
    }

    
    
    await this.setCacheData(response);
    return response;
  }

  async getHomepageReferenceData() {
    let smEnv = process.env.SM_ENV;
    let cacheKey = await this.sm.getSecretValue(smEnv,LocalConstants.HOMEPAGE_REFERENCE_DATA_VENDOR,LocalConstants.CACHE_KEY);

    let cacheData = await this.getCache(cacheKey);
    if(cacheData !== null) {
      return cacheData;
    }

    let bryzosTermsConditions = await this.dbObj.findManyWithOrComparision(this.refBryzosTermsCondition,"is_active","type",[Constants.BUYER,Constants.SELLER]);

    let response = {
      "ref_bryzos_terms_conditions": bryzosTermsConditions
    }
    
    await this.setCacheData(response,cacheKey);
    return response;
  }

  //Todo remove this method after testing, added only for SM testing purpose
  async getSM() {
   let val = await this.sm.getSecretValue('bryzos','AWS','CREDENTIAL');
   console.log("val",val);
  }

    async setCacheData(data: any,key?: string) {
    let smEnv = process.env.SM_ENV;
    let cacheKey = await this.sm.getSecretValue(smEnv,LocalConstants.REFERENCE_DATA_VENDOR,LocalConstants.CACHE_KEY);
    let stringifyResponse = JSON.stringify(data);
    let response = null;
    let compress = zlib.deflateSync(stringifyResponse)
    if(key) {
      cacheKey = key;
    }
    response = await this.cacheManager.setCache(cacheKey,compress,0);
  }

  async getCache(key?: string) {

    let response = null;
    let smEnv = process.env.SM_ENV;

    if(key == undefined) {
      key = await this.sm.getSecretValue(smEnv,LocalConstants.REFERENCE_DATA_VENDOR,LocalConstants.CACHE_KEY);
    }
    let compressedData = await this.cacheManager.getCache(key);
    if(compressedData !== undefined) {
      response = JSON.parse(zlib.inflateSync(Buffer.from(compressedData, 'base64')).toString());
    }

    return response;
  }

  async fileUploadData(productDatas){
    let response = {};
      
        const data = [];

        const ColumnsTypes = await this.getReferenceDataHeaderColumnArray();
        const headerRows = ColumnsTypes['headerRows'];
        const intColumns = ColumnsTypes['intColumns'];
        const priceColumns = ColumnsTypes['priceColumns'];
        const stringColumns = ColumnsTypes['stringColumns'];
        const booleanColumns = ColumnsTypes['booleanColumns'];

        const errorRowCount = [];
        let row = 1;
        const excelErrorCols = [];
        const excelErrorRows = {};
        const prodcutIdArr = [];

        try{
          for(const productData of productDatas){
            const rowData = {};
            const errorColCount = [];
            if(productData != null){
              for (let headerRow of headerRows) {
                const cell = productData[headerRow];
  
                let value = null;
                let product_id = null;

                //Shape_Id, Size_Group_id & Product ID NUmber Format Validation
                if(intColumns.includes(headerRow) && !excelErrorCols.includes(headerRow) && ( cell.t !== "n" || ( cell.t == "n" && !Number.isInteger(cell.v) ))){
                  excelErrorCols.push(headerRow);
                  excelErrorRows[headerRow] = [row];
                }else if( intColumns.includes(headerRow) && ( cell.t !== "n" || ( cell.t == "n" && !Number.isInteger(cell.v) )) ){
                  // excelErrorRows[headerRow].push(row);
                  excelErrorRows[headerRow] = [row];
                }else if(intColumns.includes(headerRow) && headerRow == 'Product_ID' &&  ( cell.t === "n" && Number.isInteger(cell.v))){
                  product_id = cell["w"].replace(/\s+/g, '');
                  if (prodcutIdArr.includes(product_id)) {
                    // Product ID already exists, return an error
                    excelErrorCols.push(headerRow);
                    excelErrorRows[headerRow] = [row];
                  } else {
                    // Product ID is unique, add it to the array
                    prodcutIdArr.push(product_id);
                  }
                }
                
                //Price Column Validation
                let checkPriceRelatedValidations = null;
                if( priceColumns.includes(headerRow) ){
                  checkPriceRelatedValidations = await this.checkPriceRelatedValidations(cell.v);
                  if(!checkPriceRelatedValidations && !errorColCount.includes(headerRow) ){
                    errorColCount.push(headerRow);
                    errorRowCount[headerRow] = [row];
                  }else if(!checkPriceRelatedValidations){
                    errorRowCount[headerRow].push(row);
                  }
                }

                //String Column Validation
                if( stringColumns.includes(headerRow) && !errorColCount.includes(headerRow)){
                  const getDataType = typeof(cell.v);
                  if(getDataType.toLowerCase() !== "string" || cell.t != "s"){
                    errorColCount.push(headerRow);
                    errorRowCount[headerRow] = [row];
                  }
                }else if( stringColumns.includes(headerRow) && cell.t !== "s"){
                  errorRowCount[headerRow].push(row);
                }

                // boolean column validation
                if ( booleanColumns.includes(headerRow) && ( (cell.t !== "n" && cell.t !== "b") || (cell.t === "n" && !Number.isInteger(cell.v)) || (cell.t === "b" && typeof cell.v !== 'boolean') ) ) {
                  excelErrorCols.push(headerRow);
                  excelErrorRows[headerRow] = [row];
                }else if( booleanColumns.includes(headerRow) && !errorColCount.includes(headerRow)){
                  if(cell.t === "n")
                  { 
                    cell.t = 'b';
                    cell.v = value = cell.v==0 ? false : true;
                  }
                } 


                
                if (cell && cell.t === 'n') {
                  value = cell["w"].replace(/\s+/g, '');
                }else if(cell && cell.t === 'e'){
                  value = cell.w;
                  errorColCount.push(headerRow);
                }else {
                  value = cell ? cell.v : undefined;
                }
                rowData[headerRow] = value;
              }
              if(errorColCount.length > 0){
                errorRowCount.push({[row]:errorColCount});
              }
              data.push(rowData);
              row++;
            }
          }

          if(excelErrorCols.length > 0){
            response["excel_error_message"] = excelErrorRows;
            return response;
          }
        }catch(er){
          response["error_message"] = "Something went wrong";
          return response; 
        }

        if(data.length == 0){
          response["error_message"] = "Uploaded excel contains no data";
          return response; 
        }
  
        const truncate = await this.dbObj.truncateTable(this.refProductsPreview,'reference_data_products_preview');
  
        var id = [];
        var j = 0;
        if(truncate){
          const safeCodeProducts = await this.dbObj.findMany(this.referenceDataProductsExclusionRepository, 'is_active', 'true');
          const keysToSkip = ['shape_id','created_date','created_date','id']; // Add keys to skip here
          for (const dataObj of data) {
            // Find the matching product in safeCodeProducts
            const matchingProduct = safeCodeProducts.find( product => String(product.product_id) === dataObj.Product_ID);
            if (matchingProduct) {
              // Update only the columns present in matchingProduct
              Object.keys(matchingProduct).forEach(key => {
                if (!keysToSkip.includes(key)) {
                    // Convert 1/0 to true/false for boolean keys
                    if (matchingProduct[key] === 1) {
                      dataObj[key] = true; // Set to true if value is 1
                    } else if (matchingProduct[key] === 0) {
                      dataObj[key] = false; // Set to false if value is 0
                    } else {
                      dataObj[key] = matchingProduct[key]; // Update the value for other types
                    }
                }
              });
            }                        
            var ids = await this.dbObj.saveData(dataObj,this.refProductsPreview)
            id[j] = ids;
            j++;
          }
        }
  
        if(id.length > 0){
          let refProductspreview = await this.dbObj.findAll(this.refProductsPreview);
          if (refProductspreview.length > 0) {
            response["productData"] = refProductspreview;
            if(errorRowCount){
              response["errorExcel"] = errorRowCount;
            }
          }
        }
    return response;
  }

  async getCacheData(payload)
  {
    let response=null;
    if(payload.type === process.env.REFERENCE_DATA_MEMECACHE_TYPE){
      response = await this.findAll();
    }else if(payload.type === process.env.HOMEPAGE_REFERENCE_DATA_MEMECACHE_TYPE){
      response = await this.getHomepageReferenceData();
    }
    response = {"data": response};
    return response;
  }

  async updateCache(payload)
  {
    let response=null;
    let deleteCacheData = await this.deleteCache(payload);

    if(payload.type === process.env.REFERENCE_DATA_MEMECACHE_TYPE && deleteCacheData){
        await this.findAll();
    }else if(payload.type === process.env.HOMEPAGE_REFERENCE_DATA_MEMECACHE_TYPE && deleteCacheData){
        await this.getHomepageReferenceData();
    } 
    response = {"data": "Cache Data updated successfully"};
    return response;
  }
  
  async deleteCache(payload)
  {
    let response = null;
    let smEnv = process.env.SM_ENV;
    let cacheKey = null;

    if(payload.type === process.env.REFERENCE_DATA_MEMECACHE_TYPE){
       cacheKey = await this.sm.getSecretValue(smEnv,LocalConstants.REFERENCE_DATA_VENDOR,LocalConstants.CACHE_KEY);
    }else if(payload.type === process.env.HOMEPAGE_REFERENCE_DATA_MEMECACHE_TYPE){
       cacheKey = await this.sm.getSecretValue(smEnv,LocalConstants.HOMEPAGE_REFERENCE_DATA_VENDOR,LocalConstants.CACHE_KEY);
    } 
    if(cacheKey){
      await this.cacheManager.deleteFromCache(cacheKey);
      response = {"data": "Cache data successfully deleted"};
    }

    return response;
  }

  async previewAction(action, userId, accessToken, version = null) {
    let response = null;
    if(action == "true"){
      if(!version){
        response = {"error_message":"Please enter version"};
        return response;
      }
      version = version.trim();
      const checkVersionName =  await this.dbObj.findOne(this.referenceDataProductsLogVersion,"version_name",version,false);
      if(checkVersionName){
        response = {"version_error":"The version name "+version+" for  is already in our records. Please use a different name."};
        return response;
      }

      const truncate_previous = await this.dbObj.truncateTable(this.refProductsPrevious,'reference_data_products_previous');
      let refProductsWidget = await this.dbObj.findAll(this.refProductsWidget);
      const backup = await this.dbObj.saveData(refProductsWidget,this.refProductsPrevious);
      if(backup){
        //we truncate existing table to make room for new entries
        const truncate = await this.dbObj.truncateTable(this.refProductsWidget,'reference_data_products_widget');
        //get entries from preview table to insert in products table
        let refProductsPreview = await this.dbObj.findAll(this.refProductsPreview);
        //insert entries from preview to products table
        const copy = await this.dbObj.saveData(refProductsPreview,this.refProductsWidget);
        
        //update homepage products table also with latest version data
        let truncateHomepage = await this.dbObj.truncateTable(this.referenceDataProductsHomepageRepository,'reference_data_products_homepage');
        const homePageProducts = await this.dbObj.saveData(refProductsPreview,this.referenceDataProductsHomepageRepository);

        if(copy){
          await this.dbObj.truncateTable(this.refProductsPreview,'reference_data_products_preview');

          let versionLog = {};
          await this.dbObj.updateByMultipleWhere({"is_active":false},{"status":LocalConstants.PREVIOUS},this.referenceDataProductsLogVersion);
          await this.dbObj.updateByMultipleWhere({"status":LocalConstants.PREVIOUS},{"status":LocalConstants.ACTIVE},this.referenceDataProductsLogVersion);
          versionLog["status"] = LocalConstants.ACTIVE;
          versionLog["version_name"] = version;
          await this.dbObj.saveData(versionLog,this.referenceDataProductsLogVersion);

          await this.uploadHomepageEncryptedProductDataToS3();

          //send email if products changed
          this.awsQueue.sendReferenceDataProductUpdateEmail(version);

          const internalNotifcation = await this.dbObj.findOneByMultipleWhere(this.referenceDataDesktopNotificationRepository,{"notification_event" : LocalConstants.INTERNAL_NOTIFICATION});
          if(internalNotifcation){
            const callNotificationResponse = await this.callNotificationService({ notification_event: internalNotifcation.notification_event, action: internalNotifcation.action, priority: internalNotifcation.priority.toUpperCase(), message: internalNotifcation.notification_message }, userId, accessToken);
            if (typeof callNotificationResponse === "object" && callNotificationResponse[responseErrorTag]) {
              return callNotificationResponse;
            }
          }
          
          this.productData = refProductsPreview;
          response = version+" Data Imported Successfully";
        }

      }else{
        response = {"error_message":"There is a issue while taking bakcup of previous data"};
      }

    }else if(action == "false"){
      const truncate = await this.dbObj.truncateTable(this.refProductsPreview,'reference_data_products_preview');
      response = "The import of new data has been cancelled.";
    }
    return response;
  }

  async callNotificationService(payload, adminId, accessToken) {
    const adminLogNotificationDTO = { admin_id: adminId, notification_event: payload.notification_event };

    const securityData = await this.dbObj.findOne(this.referenceDataSettings,"name",Constants.SECURITY_SECRET_KEY);
    let securityHashKey : string;
    if(securityData){
      securityHashKey = createHash('sha256').update(securityData.value).digest('hex');
    }
    
    payload = { [payloadTag]: payload };
    try {
      const response = (await axios.post(
        `${process.env.NOTIFICATION_SERVER_URL}/sendAdminDashboardNotification`,
        payload,
        { headers: { accesstoken: accessToken,origin: process.env.UI_ORIGIN,referer: process.env.UI_REFERER,security: securityHashKey }, params: { adminId: adminId } },
      )).data;

      if (!response || (typeof response === "object" && response[responseErrorTag])) {
        return {
          [responseErrorTag]: 'Sorry, unable to call notification service',
        };
      }

      this.dbObj.saveData(adminLogNotificationDTO, this.adminLogNotificationRepository);

      return response;

    } catch (error) {
      return { [responseErrorTag]: 'Sorry, unable to call notification service' };
    }
  }

  async getReferenceDataHeaderColumnArray(){
    const headerRows = [
      "Shape_ID",
      "Size_Group_ID",
      "Product_ID",
      "LBS_FT",
      "Order_Increment_Ft",
      "Order_Increment_Ea",
      "Order_Increment_Lb",
      "Order_Increment_CWT",
      "Order_Increment_Net_Ton",
      "Bucket",
      "Key1",
      "Key2",
      "Key3",
      "Key4",
      "Key5",
      "Key6",
      "Key7",
      "Key8",
      "Key9",
      "Key10",
      "Key11",
      "Key12",
      "Key13",
      "Key14",
      "Key15",
      "Key16",
      "Key17",
      "Key18",
      "Key19",
      "Key20",
      "Key21",
      "Key22",
      "Key23",
      "Key24",
      "UI_Description",
      "QUM_Dropdown_Options",
      "PUM_Dropdown_Options",
      "Neutral_Pricing_Ft",
      "Neutral_Pricing_Ea",
      "Neutral_Pricing_LB",
      "Neutral_Pricing_CWT",
      "Neutral_Pricing_Net_Ton",
      "Buyer_Pricing_Ft",
      "Buyer_Pricing_Ea",
      "Buyer_Pricing_LB",
      "Buyer_Pricing_CWT",
      "Buyer_Pricing_Net_Ton",
      "Seller_Pricing_Ft",
      "Seller_Pricing_Ea",
      "Seller_Pricing_LB",
      "Seller_Pricing_CWT",
      "Seller_Pricing_Net_Ton",
      "domestic_material_only"
    ]

    const intColumns = [
      "Shape_ID",
      "Size_Group_ID",
      "Product_ID"
    ];

    const priceColumns = [
      "LBS_FT",
      "Order_Increment_Ft",
      "Order_Increment_Ea",
      "Order_Increment_Lb",
      "Order_Increment_CWT",
      "Order_Increment_Net_Ton",
      "Neutral_Pricing_Ft",
      "Neutral_Pricing_Ea",
      "Neutral_Pricing_LB",
      "Neutral_Pricing_CWT",
      "Neutral_Pricing_Net_Ton",
      "Buyer_Pricing_Ft",
      "Buyer_Pricing_Ea",
      "Buyer_Pricing_LB",
      "Buyer_Pricing_CWT",
      "Buyer_Pricing_Net_Ton",
      "Seller_Pricing_Ft",
      "Seller_Pricing_Ea",
      "Seller_Pricing_LB",
      "Seller_Pricing_CWT",
      "Seller_Pricing_Net_Ton"
    ];

    const stringColumns = [
      "UI_Description",
      "QUM_Dropdown_Options",
      "PUM_Dropdown_Options",
    ];

    const booleanColumns = [
      "domestic_material_only"
    ];
    const columnJson = {};
    columnJson['headerRows'] = headerRows;
    columnJson['intColumns'] = intColumns;
    columnJson['priceColumns'] = priceColumns;
    columnJson['stringColumns'] = stringColumns;
    columnJson['booleanColumns'] = booleanColumns;

    return columnJson;
  }
  
  async revertReferenceProducts(action,adminId, accessToken){
    let response = null;
    let adminLog = {};
    if(action == "true"){
      const previousVersion = await this.dbObj.findOne(this.referenceDataProductsLogVersion,"status",LocalConstants.PREVIOUS);
      let refProductsPrevious = await this.dbObj.findAll(this.refProductsPrevious);
      if(previousVersion && refProductsPrevious.length > 0){
        //load data in products table
        await this.dbObj.truncateTable(this.refProductsWidget,'reference_data_products_widget');
        await this.dbObj.saveData(refProductsPrevious,this.refProductsWidget);
        
        //load previous version data in homepage product table also
        await this.dbObj.truncateTable(this.referenceDataProductsHomepageRepository,'reference_data_products_homepage');
        await this.dbObj.saveData(refProductsPrevious,this.referenceDataProductsHomepageRepository);

        //after complete load, update product's for safe code.
        let safeCodeProducts = await this.dbObj.findMany(this.referenceDataProductsExclusionRepository, 'is_active', 'true');
        if (safeCodeProducts.length > 0) {
          const excludedColumns = ['id', 'shape_id', 'product_id', 'created_date','is_active', 'time_stamp']; // Add any columns you want to exclude

          for (const scp of safeCodeProducts) {
            const updateDto = Object.fromEntries(
              Object.entries(scp)
                .filter(([key]) => !excludedColumns.includes(key)) // Exclude unwanted keys
              );
              // Ensure boolean value is correctly set
              if (scp.is_safe_product_code !== undefined) {
                updateDto.is_safe_product_code = scp.is_safe_product_code ? true : false; // Convert to 1 or 0
              }
              await this.dbObj.updateByMultipleWhere(
                updateDto,
                { 'Product_ID': scp.product_id }, this.refProductsWidget
            );
          }
        }

        adminLog["admin_id"] = adminId;
        await this.dbObj.saveData(adminLog,this.adminLogReferenceDataProductRevert);

        //truncate table with previous data to avoid multiple revert action with same data
        await this.dbObj.truncateTable(this.refProductsWidget,'reference_data_products_previous');

        //update product log version table
        await this.dbObj.updateByMultipleWhere({"is_active":false},{"status":LocalConstants.ACTIVE},this.referenceDataProductsLogVersion);
        await this.dbObj.updateByMultipleWhere({"status":LocalConstants.ACTIVE},{"version_name":previousVersion.version_name,"id":previousVersion.id},this.referenceDataProductsLogVersion);
        
        await this.uploadHomepageEncryptedProductDataToS3();

        //send email if product reverted
        this.awsQueue.sendReferenceDataProductUpdateEmail(previousVersion.version_name);

        const internalNotifcation = await this.dbObj.findOneByMultipleWhere(this.referenceDataDesktopNotificationRepository,{"notification_event" : LocalConstants.INTERNAL_NOTIFICATION});
        if(internalNotifcation){
          const callNotificationResponse = await this.callNotificationService({ notification_event: internalNotifcation.notification_event, action: internalNotifcation.action, priority: internalNotifcation.priority.toUpperCase(), message: internalNotifcation.notification_message }, adminId, accessToken);
          if (typeof callNotificationResponse === "object" && callNotificationResponse[responseErrorTag]) {
            return callNotificationResponse;
          }
        }

        this.productData = refProductsPrevious;

        response = previousVersion.version_name+" Data Reverted Successfully";
      }else{
        response = {"error_message":"No Data Found"};
      }
    }else if(action == "false"){
      response = "The operation of reverting the products has been cancelled.";
    }
    return response;
  }

  async getRefDatasVersion(){
    let response = null;
    const versions = await this.dbObj.findManyByWhereIn(this.referenceDataProductsLogVersion,"status",[LocalConstants.ACTIVE,LocalConstants.PREVIOUS]);
    if(versions){
      response = versions;
    }
    return response;
  }

  async checkPriceRelatedValidations(value){
    let response = false;
    if(!value){ return response };
    let typeOf = typeof(value);
    let finalValue = value;
    if(typeOf.toLowerCase() == "string" && typeOf.toLocaleLowerCase() != "number"){
      finalValue = value.replace(/[!@#$%^&*,]/g,"");
    }
    finalValue = Number(finalValue);
    if(finalValue && finalValue > 0){
      response = true;
    }
    return response;
  }

  async findAllHomepageProducts(){
    let response = null;
    let prods = await this.dbObj.findAll(this.referenceDataProductsHomepageRepository);
    if (prods.length > 0) {
      response = prods
    }
    return response;
  }

  async getHomePageProductReferenceData(){
    let refProducts = await this.findAllHomepageProducts();
    if (!refProducts)
      return null;
    
    refProducts = JSON.stringify(refProducts);
    const encryptedData = await this.sm.encryptString(refProducts);
    return encryptedData;
  }

  //build s3 file and update the file with encrypted data.

  async uploadHomepageEncryptedProductDataToS3(){

    let bucketName = process.env.BRYZOS_ASSETS_BUCKET;
    let fileName = process.env.HOMEPAGE_ENCRYPTED_PRODUCTS_FILE;

    let encryptedData =  await this.getHomePageProductReferenceData(); 

    this.awsUtility.updateS3File(fileName,bucketName,encryptedData);

    return "Home page products updated successfully!";
  }

  async searchProducts(searchKeyword, userId) {

    const products = Object.values(this.productData); // Replace with your actual array of products
    const searchData = searchKeyword;
    const searchStrings = await this.getValidSearchData(searchData);
    const filteredProducts = await this.searchMatchingProducts(products, searchStrings, searchData);
    const user = await this.dbObj.findOne(this.userRepository, 'id', userId);
    const userType = user.type;

    // Process the products based on user type
    const cleanedProducts = filteredProducts.map((product) =>
      this.filterFieldsByUserType(product, userType)
    );    
    return cleanedProducts;

  }

  async searchMatchingProducts(products: any[], searchStrings: string[], searchData: string) {

    if (Array.isArray(products) && (searchData !== '' && searchData.length >= 2)) {
      return products.filter((product) => {
        return searchStrings.every((searchString) => {
          return Object.keys(product).some((key) => {
            if (key.startsWith('Key')) {
              const keyElement = product[key];
              if (keyElement === '' || keyElement === null) {
                return false;
              }
              return keyElement.toLowerCase().startsWith(searchString);
            }
            return false;
          });
        });
      });
    }
    return [];
  }

  async getValidSearchData(searchString: string) {
    const regex = /[^\w'./"-]+/g;
    const words = searchString.toLowerCase().split(regex).filter(Boolean);

    const phrasesToConcatenate = [
      "type b",
      "sch 40",
      "sch 80",
      "1 1/4",
      "1 1/2",
      "1 1/8",
      "1 3/8",
      "1 5/8",
      "1 7/8",
      "1 3/4",
      "2 1/2",
      "2 1/4",
      "2 1/8",
      "2 3/4",
      "2 7/8",
    ];

    for (let i = 0; i < words.length - 1; i++) {
      const currentAndNextWordConcat = words[i] + " " + words[i + 1];
      if (phrasesToConcatenate.includes(currentAndNextWordConcat)) {
        words[i] = currentAndNextWordConcat;
        words.splice(i + 1, 1);
      } else if (words[i].includes('x') && i > 0 && i < words.length - 1) {
        words.splice(i, 1); // delete x
      }
    }

    const uniqueWords = [...new Set(words)];
    return uniqueWords;
  }

  async getHomepageSafeConfig() {
    let response = null;
    let homepageSafeConfig = await this.dbObj.findAll(this.homepageSafeConfigRepository);
    if(homepageSafeConfig.length > 0) {
      response = homepageSafeConfig;
    }
    return response;
  }

  async getSecurityToken(){
    try{
      let secretKeyData = await this.dbObj.findOne(this.referenceDataSettings,"name",Constants.SECURITY_SECRET_KEY);
      const key = secretKeyData.value;
      const getEncryptedSecurityKey = await this.sm.encryptString(key);
      return getEncryptedSecurityKey;
    }catch(error){
      BryzosLogger.log(JSON.stringify({"Error":error}), process.env.LOGGLY_ERROR_TAG);
      return {error_message :'Something Went Wrong!'};
    }
  }

  async getVideoLibraryTags() {
    let orderBy = { created_date  : "DESC" }
    let columnConditions = [{ columnName: 'is_active', operator: '=', value: true },{ columnName: 'is_internal_tag', operator: '=', value: false }]
    let refVideoLibraryTag = await this.dbObj.findManyWithDynamiConditionsAndOperators(this.referenceDataVideoLibraryTagRepository, columnConditions, '*', orderBy);
    return refVideoLibraryTag.length > 0 ? refVideoLibraryTag : null;
  }

  private filterFieldsByUserType(product: any, userType: string) {
    // Common fields for all user types
    const commonFields = {
      Product_ID: product.Product_ID,
      UI_Description: product.UI_Description,
      domestic_material_only: product.domestic_material_only,
    };
  
    // Define pricing keys based on user type prefixes
    const pricingKeyPrefixes = {
      BUYER: 'Buyer_Pricing',
      SELLER: 'Seller_Pricing',
      NEUTRAL: 'Neutral_Pricing',
    };
  
    // Get the pricing prefix based on user type
    const prefix = pricingKeyPrefixes[userType];
  
    // If user type is invalid, return only common fields
    if (!prefix) {
      return commonFields;
    }
  
    // Construct pricing fields dynamically based on the prefix
    const pricingFields = {
      [`${prefix}_Ft`]: product[`${prefix}_Ft`],
      [`${prefix}_Ea`]: product[`${prefix}_Ea`],
      [`${prefix}_LB`]: product[`${prefix}_LB`],
      [`${prefix}_CWT`]: product[`${prefix}_CWT`],
      [`${prefix}_Net_Ton`]: product[`${prefix}_Net_Ton`],
    };
  
    // Merge common fields with pricing fields
    return {
      ...commonFields,
      ...pricingFields,
    };
  }
}
