import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ReferenceDataBryzosTermsConditions, User, UserWebhookSubscription } from '@bryzos/extended-widget-library';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CognitoAuthService, DataBaseService } from '@bryzos/base-library';
import { Constants } from 'src/Constants';
import { ExternalUserLoginDto } from 'src/user/dto/user.dto';


@Injectable()
export class ExternalApiService {
  private dbObj = new DataBaseService();
  constructor(
    private readonly cognitoAuthService:CognitoAuthService,
    @InjectRepository(ReferenceDataBryzosTermsConditions) private readonly refBryzosTermsCondition: Repository<ReferenceDataBryzosTermsConditions>,
    @InjectRepository(UserWebhookSubscription) private readonly userWebhookSubscription: Repository<UserWebhookSubscription>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,

  ) 
  {}

  async findAll() {
    let bryzosTermsConditions = await this.dbObj.findManyWithOrComparision(this.refBryzosTermsCondition,"is_active","type",[Constants.BUYER,Constants.SELLER]);
    let response = {
      "ref_bryzos_terms_conditions": bryzosTermsConditions,
    }
    return response;
  }

  async getAccessToken(loginDto: ExternalUserLoginDto) {
    let response = await this.cognitoAuthService.authenticateUser(loginDto.email, loginDto.password);
    const userData = await this.dbObj.findOne(this.userRepository,"email_id",loginDto.email);
    if(!userData){
      throw new UnauthorizedException('User not found')
    }
    response["user_type"] = userData.type
    return response;
  }

  async subscribeWebhook(userId, payload)
  {
    let response = null;
    let user = await this.dbObj.findOne(this.userRepository, 'id', userId);
    if(!user) {
      return { "error_message": "User not found" };
    }

    let companyId = user.company_id;
    //deactivate if any other webhook url's exists
    await this.dbObj.updateByMultipleWhere({'is_active': false},{'company_id': companyId, 'event': payload.event}, this.userWebhookSubscription);

    //TODO: get webhook events from DB
    let webhookId = await this.dbObj.saveWithOutMapper({'user_id': userId,'company_id': companyId, 'webhook_url': payload.webhook_url, 'event': payload.event}, userId, this.userWebhookSubscription);

    if(webhookId)
      response = "Successfully subscribed to webhook. Your webhook id is "+ webhookId;

    return response;
  }

  async unsubscribeWebhook(userId, webhookId)
  {
    let response = null;
    let user = await this.dbObj.findOne(this.userRepository, 'id', userId);
    if(!user) {
      return { "error_message": "User not found" };
    }

    let companyId = user.company_id;
    //unsubscribe webhook url's
    let updateResponse = await this.dbObj.updateByMultipleWhere({'is_active': false,'is_subscribed': false},{'company_id': companyId, 'id': webhookId, 'user_id': userId}, this.userWebhookSubscription);

    if(updateResponse) 
      response = "Successfully unsubscribed to webhook.";
    else 
      response = "This webhook does not belong to you";
    

    return response;
  }
}
