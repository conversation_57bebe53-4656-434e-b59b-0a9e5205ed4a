export class UserBuyingPreferenceMapper {

    public roundOffFields = [];//['price_per_lb', 'price_per_cwt', 'price_per_nton', 'price_per_mton', 'price_per_ft', 'price_per_ea'];
    public dateFields = [];

    public MappingJson = {
        "company_address_line1": {
            "address": "line1"
        },
        "company_address_line2": {
            "address": "line2"
        },
        "company_address_city": {
            "address": "city"
        },
        "company_address_state_id": {
            "address": "state_id"
        },
        "company_address_zip": {
            "address": "zip"
        },
        "company_name": "company_name",
        "first_name": "first_name",
        "last_name": "last_name",
        "email_id": "email_id",
        "phone": "phone",
        "delivery_address_line1": {
            "delivery_address": "line1"
        },
        "delivery_address_line2": {
            "delivery_address": "line2"
        },
        "delivery_address_city": {
            "delivery_address": "city"
        },
        "delivery_address_state_id": {
            "delivery_address": "state_id"
        },
        "delivery_address_zip": {
            "delivery_address": "zip"
        },
        "delivery_days_add_value": "delivery_days_add_value",
        "send_invoices_to": "send_invoices_to",
        "shipping_docs_to": "shipping_docs_to",
        "default_payment_method": "default_payment_method",
        "client_company": "client_company",
        "company_id": "company_id",
        "super_admin_user_id"  : "super_admin_user_id"
    };
}

//resale_certificate
export class UserResaleCertificateMapper {
    public roundOffFields = [];
    public dateFields = [];

    public MappingJson = {
        "state_id": "state_id",
        "expiration_date": "expiration_date",
        "cerificate_url_s3": "cerificate_url_s3",
        "file_name": "file_name",
        "status": "status",
        "company_id": "company_id",
        "super_admin_user_id"  : "super_admin_user_id"
    };
}

export class UserDeliveryReceivingAvailibitityDetailsMapper {

    public roundOffFields = [];
    public dateFields = ['created_date'];

    public MappingJson = {
        "day": "day",
        "from": "from",
        "to": "to",
        "display_name": "display_name",
        "is_user_available": "is_user_available"
    };

    public booleanColumns = ['is_user_available']
}

//ach_credit
export class UserAchCreditMapper {
    public roundOffFields = [];
    public dateFields = [];

    public MappingJson = {
        "bank_name": "bank_name",
        "routing_number": "routing_number",
        "account_number": "account_number",
        "truvault_document_id": "truvault_document_id"
    };
}
